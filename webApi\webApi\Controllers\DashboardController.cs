using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// Controller لإدارة بيانات لوحة التحكم الشاملة
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DashboardController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<DashboardController> _logger;
        private readonly ILoggingService _loggingService;

        public DashboardController(TasksDbContext context, ILogger<DashboardController> logger, ILoggingService loggingService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// الحصول على إحصائيات المهام الشاملة
        /// </summary>
        [HttpGet("task-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTaskStatistics()
        {
            try
            {
                var totalTasks = await _context.Tasks.Where(t => !t.IsDeleted).CountAsync();
                var completedTasks = await _context.Tasks.Where(t => !t.IsDeleted && t.Status == "completed").CountAsync();
                var pendingTasks = await _context.Tasks.Where(t => !t.IsDeleted && t.Status == "pending").CountAsync();
                var inProgressTasks = await _context.Tasks.Where(t => !t.IsDeleted && t.Status == "in_progress").CountAsync();
                var overdueTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.DueDate.HasValue && t.DueDate < DateTimeOffset.UtcNow.ToUnixTimeSeconds() && t.Status != "completed" && t.Status != "cancelled")
                    .CountAsync();

                // إحصائيات حسب الحالة
                var tasksByStatus = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .GroupBy(t => t.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Status, x => x.Count);

                // إحصائيات حسب الأولوية
                var tasksByPriority = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .GroupBy(t => t.Priority)
                    .Select(g => new { Priority = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Priority, x => x.Count);

                // إحصائيات حسب الأقسام
                var tasksByDepartment = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.DepartmentId.HasValue)
                    .Include(t => t.Department)
                    .GroupBy(t => t.Department!.Name)
                    .Select(g => new { Department = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Department, x => x.Count);

                // إحصائيات حسب المكلف
                var tasksByAssignee = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.AssigneeId.HasValue)
                    .Include(t => t.Assignee)
                    .GroupBy(t => t.Assignee!.Name ?? t.Assignee.Username)
                    .Select(g => new { Assignee = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Assignee, x => x.Count);

                // إحصائيات حسب المنشئ
                var tasksByCreator = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Include(t => t.Creator)
                    .GroupBy(t => t.Creator.Name ?? t.Creator.Username ?? "غير محدد")
                    .Select(g => new { Creator = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Creator, x => x.Count);

                var result = new
                {
                    totalTasks,
                    completedTasks,
                    pendingTasks,
                    inProgressTasks,
                    overdueTasks,
                    tasksByStatus,
                    tasksByPriority,
                    tasksByDepartment,
                    tasksByAssignee,
                    tasksByCreator
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب إحصائيات المهام للوحة التحكم",
                    "GetTaskStatistics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات المهام");
                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب إحصائيات المهام",
                    ex.ToString()
                );
                return StatusCode(500, "حدث خطأ في جلب إحصائيات المهام");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المستخدمين
        /// </summary>
        [HttpGet("user-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUserStatistics()
        {
            try
            {
                var totalUsers = await _context.Users.Where(u => !u.IsDeleted).CountAsync();
                var activeUsers = await _context.Users.Where(u => !u.IsDeleted && u.IsActive).CountAsync();
                var inactiveUsers = totalUsers - activeUsers;

                // إحصائيات حسب الأقسام
                var usersByDepartment = await _context.Users
                    .Where(u => !u.IsDeleted && u.DepartmentId.HasValue)
                    .Include(u => u.Department)
                    .GroupBy(u => u.Department!.Name)
                    .Select(g => new { Department = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Department, x => x.Count);

                // إحصائيات حسب الأدوار
                var usersByRole = await _context.Users
                    .Where(u => !u.IsDeleted && u.Role != null)
                    .Include(u => u.Role)
                    .GroupBy(u => u.Role!.Name)
                    .Select(g => new { Role = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Role, x => x.Count);

                // إحصائيات الوصول للمهام حسب المستخدم
                var taskAccessByUser = await _context.TaskAccessUsers
                    .Include(tau => tau.User)
                    .GroupBy(tau => tau.User!.Name ?? tau.User.Username ?? "غير محدد")
                    .Select(g => new { User = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.User, x => x.Count);

                var result = new
                {
                    totalUsers,
                    activeUsers,
                    inactiveUsers,
                    usersByDepartment,
                    usersByRole,
                    taskAccessByUser
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب إحصائيات المستخدمين للوحة التحكم",
                    "GetUserStatistics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات المستخدمين");
                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب إحصائيات المستخدمين",
                    ex.ToString()
                );
                return StatusCode(500, "حدث خطأ في جلب إحصائيات المستخدمين");
            }
        }

        /// <summary>
        /// الحصول على مصفوفة المستخدم والحالة (للمخططات المزدوجة)
        /// </summary>
        [HttpGet("user-status-matrix")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUserStatusMatrix()
        {
            try
            {
                var matrix = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.AssigneeId.HasValue)
                    .Include(t => t.Assignee)
                    .GroupBy(t => new {
                        UserName = t.Assignee!.Name ?? t.Assignee.Username ?? "غير محدد",
                        Status = t.Status
                    })
                    .Select(g => new { 
                        User = g.Key.UserName,
                        Status = g.Key.Status,
                        Count = g.Count() 
                    })
                    .ToListAsync();

                // تنظيم البيانات في شكل مصفوفة
                var result = matrix
                    .GroupBy(m => m.User)
                    .Select(g => new
                    {
                        User = g.Key,
                        StatusCounts = g.ToDictionary(x => x.Status, x => x.Count),
                        TotalTasks = g.Sum(x => x.Count)
                    })
                    .ToList();

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب مصفوفة المستخدم والحالة للوحة التحكم",
                    "GetUserStatusMatrix API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مصفوفة المستخدم والحالة");
                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب مصفوفة المستخدم والحالة",
                    ex.ToString()
                );
                return StatusCode(500, "حدث خطأ في جلب مصفوفة المستخدم والحالة");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الوصول للمهام مع دعم الفلاتر
        /// </summary>
        [HttpGet("task-access-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTaskAccessStatistics(
            string? status = null,
            string? priority = null,
            int? assigneeId = null,
            int? departmentId = null,
            long? created_at_start = null,
            long? created_at_end = null,
            long? start_date_start = null,
            long? start_date_end = null,
            long? due_date_start = null,
            long? due_date_end = null)
        {
            try
            {
                // دالة مساعدة لتطبيق فلاتر المهام على task_access_users
                var filteredTaskAccessQuery = _context.TaskAccessUsers
                    .Include(tau => tau.User)
                    .Include(tau => tau.Task)
                    .Where(tau => !tau.Task!.IsDeleted)
                    .Where(tau =>
                        (string.IsNullOrEmpty(status) || tau.Task!.Status == status) &&
                        (string.IsNullOrEmpty(priority) || tau.Task!.Priority == priority) &&
                        (!departmentId.HasValue || tau.Task!.DepartmentId == departmentId.Value) &&
                        (!created_at_start.HasValue || tau.Task!.CreatedAt >= created_at_start.Value) &&
                        (!created_at_end.HasValue || tau.Task!.CreatedAt <= created_at_end.Value) &&
                        (!start_date_start.HasValue || (tau.Task!.StartDate.HasValue && tau.Task.StartDate >= start_date_start.Value)) &&
                        (!start_date_end.HasValue || (tau.Task!.StartDate.HasValue && tau.Task.StartDate <= start_date_end.Value)) &&
                        (!due_date_start.HasValue || (tau.Task!.DueDate.HasValue && tau.Task.DueDate >= due_date_start.Value)) &&
                        (!due_date_end.HasValue || (tau.Task!.DueDate.HasValue && tau.Task.DueDate <= due_date_end.Value))
                    );

                // إحصائيات الوصول للمهام حسب المستخدم
                var userTaskAccess = await filteredTaskAccessQuery
                    .GroupBy(tau => tau.User!.Name ?? tau.User.Username ?? "غير محدد")
                    .Select(g => new { User = g.Key, TaskCount = g.Count() })
                    .ToDictionaryAsync(x => x.User, x => x.TaskCount);

                // إحصائيات المساهمين في المهام
                var taskContributors = await filteredTaskAccessQuery
                    .GroupBy(tau => tau.TaskId)
                    .Select(g => new { TaskId = g.Key, ContributorCount = g.Count() })
                    .ToListAsync();

                var avgContributorsPerTask = taskContributors.Any()
                    ? taskContributors.Average(tc => tc.ContributorCount)
                    : 0;

                // عدد السجلات المفلترة من task_access_users
                var filteredTaskAccessRecords = await filteredTaskAccessQuery.CountAsync();

                var result = new
                {
                    userTaskAccess,
                    totalTaskAccessRecords = filteredTaskAccessRecords,
                    averageContributorsPerTask = Math.Round(avgContributorsPerTask, 2),
                    tasksWithMultipleContributors = taskContributors.Count(tc => tc.ContributorCount > 1)
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب إحصائيات الوصول للمهام للوحة التحكم",
                    "GetTaskAccessStatistics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات الوصول للمهام");
                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب إحصائيات الوصول للمهام",
                    ex.ToString()
                );
                return StatusCode(500, "حدث خطأ في جلب إحصائيات الوصول للمهام");
            }
        }

        /// <summary>
        /// الحصول على تحليل الأداء حسب الأقسام
        /// </summary>
        [HttpGet("department-performance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetDepartmentPerformance()
        {
            try
            {
                var departmentPerformance = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.DepartmentId.HasValue)
                    .Include(t => t.Department)
                    .GroupBy(t => t.Department!.Name)
                    .Select(g => new
                    {
                        Department = g.Key,
                        TotalTasks = g.Count(),
                        CompletedTasks = g.Count(t => t.Status == "completed"),
                        InProgressTasks = g.Count(t => t.Status == "in_progress"),
                        PendingTasks = g.Count(t => t.Status == "pending"),
                        OverdueTasks = g.Count(t => t.DueDate.HasValue && 
                                              t.DueDate < DateTimeOffset.UtcNow.ToUnixTimeSeconds() && 
                                              t.Status != "completed"),
                        AverageCompletionPercentage = g.Average(t => t.CompletionPercentage)
                    })
                    .ToListAsync();

                // حساب معدل الإنجاز لكل قسم
                var result = departmentPerformance.Select(dp => new
                {
                    dp.Department,
                    dp.TotalTasks,
                    dp.CompletedTasks,
                    dp.InProgressTasks,
                    dp.PendingTasks,
                    dp.OverdueTasks,
                    CompletionRate = dp.TotalTasks > 0 ? Math.Round((double)dp.CompletedTasks / dp.TotalTasks * 100, 2) : 0,
                    AverageCompletionPercentage = Math.Round(dp.AverageCompletionPercentage, 2)
                }).ToList();

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب تحليل أداء الأقسام للوحة التحكم",
                    "GetDepartmentPerformance API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تحليل أداء الأقسام");
                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب تحليل أداء الأقسام",
                    ex.ToString()
                );
                return StatusCode(500, "حدث خطأ في جلب تحليل أداء الأقسام");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المساهمين في المهام
        /// </summary>
        [HttpGet("task-contributors")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTaskContributors()
        {
            try
            {
                // إحصائيات المساهمين حسب المستخدم
                var contributorStats = await _context.TaskAccessUsers
                    .Include(tau => tau.User)
                    .Include(tau => tau.Task)
                    .Where(tau => !tau.Task!.IsDeleted && !tau.User!.IsDeleted)
                    .GroupBy(tau => new { tau.UserId, tau.User!.Name })
                    .Select(g => new
                    {
                        UserId = g.Key.UserId,
                        UserName = g.Key.Name,
                        TotalContributions = g.Count(),
                        CompletedTasks = g.Count(tau => tau.Task!.Status == "completed"),
                        PendingTasks = g.Count(tau => tau.Task!.Status == "pending"),
                        InProgressTasks = g.Count(tau => tau.Task!.Status == "in_progress"),
                        CancelledTasks = g.Count(tau => tau.Task!.Status == "cancelled"),
                        WaitingForInfoTasks = g.Count(tau => tau.Task!.Status == "waiting_for_info")
                    })
                    .ToListAsync();

                // إحصائيات المهام حسب عدد المساهمين
                var tasksByContributorCount = await _context.TaskAccessUsers
                    .Include(tau => tau.Task)
                    .Where(tau => !tau.Task!.IsDeleted)
                    .GroupBy(tau => tau.TaskId)
                    .Select(g => new { TaskId = g.Key, ContributorCount = g.Count() })
                    .GroupBy(x => x.ContributorCount)
                    .Select(g => new { ContributorCount = g.Key, TaskCount = g.Count() })
                    .ToListAsync();

                var result = new
                {
                    contributorStats,
                    tasksByContributorCount,
                    totalContributors = contributorStats.Count,
                    averageContributionsPerUser = contributorStats.Any() ?
                        Math.Round(contributorStats.Average(c => c.TotalContributions), 2) : 0,
                    mostActiveContributor = contributorStats.OrderByDescending(c => c.TotalContributions).FirstOrDefault()
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب إحصائيات المساهمين في المهام",
                    "GetTaskContributors API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات المساهمين");

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب إحصائيات المساهمين",
                    ex.ToString()
                );

                return StatusCode(500, "حدث خطأ في جلب إحصائيات المساهمين");
            }
        }

        /// <summary>
        /// الحصول على مصفوفة المستخدم والحالة المفصلة
        /// </summary>
        [HttpGet("user-task-matrix")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUserTaskMatrix()
        {
            try
            {
                // مصفوفة المستخدمين والمهام حسب الحالة (من TaskAccessUsers)
                var userTaskMatrix = await _context.TaskAccessUsers
                    .Include(tau => tau.User)
                    .Include(tau => tau.Task)
                    .Where(tau => !tau.Task!.IsDeleted && !tau.User!.IsDeleted)
                    .GroupBy(tau => new {
                        UserId = tau.UserId,
                        UserName = tau.User!.Name,
                        Status = tau.Task!.Status
                    })
                    .Select(g => new {
                        g.Key.UserId,
                        g.Key.UserName,
                        g.Key.Status,
                        Count = g.Count()
                    })
                    .ToListAsync();

                // مصفوفة المكلفين والمهام حسب الحالة (من Assignee)
                var assigneeTaskMatrix = await _context.Tasks
                    .Include(t => t.Assignee)
                    .Where(t => !t.IsDeleted && t.AssigneeId.HasValue)
                    .GroupBy(t => new {
                        UserId = t.AssigneeId,
                        UserName = t.Assignee!.Name,
                        Status = t.Status
                    })
                    .Select(g => new {
                        g.Key.UserId,
                        g.Key.UserName,
                        g.Key.Status,
                        Count = g.Count()
                    })
                    .ToListAsync();

                var result = new
                {
                    contributorMatrix = userTaskMatrix,
                    assigneeMatrix = assigneeTaskMatrix,
                    totalContributorRecords = userTaskMatrix.Count,
                    totalAssigneeRecords = assigneeTaskMatrix.Count,
                    uniqueContributors = userTaskMatrix.Select(x => x.UserId).Distinct().Count(),
                    uniqueAssignees = assigneeTaskMatrix.Select(x => x.UserId).Distinct().Count()
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب مصفوفة المستخدم والحالة المفصلة",
                    "GetUserTaskMatrix API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مصفوفة المستخدم والحالة");

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "ERROR",
                    "خطأ في جلب مصفوفة المستخدم والحالة",
                    ex.ToString()
                );

                return StatusCode(500, "حدث خطأ في جلب مصفوفة المستخدم والحالة");
            }
        }

        /// <summary>
        /// الحصول على تحليلات الأداء الزمني للمهام
        /// </summary>
        [HttpGet("time-performance-analytics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTimePerformanceAnalytics()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var thirtyDaysAgo = currentTime - (30 * 24 * 60 * 60);

                // متوسط وقت إنجاز المهام (بالأيام)
                var completedTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.CompletedAt.HasValue && t.CreatedAt > 0)
                    .Select(t => new {
                        t.Id,
                        t.CreatedAt,
                        t.CompletedAt,
                        CompletionDays = (t.CompletedAt.Value - t.CreatedAt) / (24 * 60 * 60.0)
                    })
                    .ToListAsync();

                var averageCompletionTime = completedTasks.Any()
                    ? Math.Round(completedTasks.Average(t => t.CompletionDays), 2)
                    : 0;

                // معدل الإنجاز في آخر 30 يوم
                var recentCompletions = completedTasks
                    .Where(t => t.CompletedAt >= thirtyDaysAgo)
                    .Count();

                // المهام المتأخرة
                var overdueTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.DueDate.HasValue &&
                               t.DueDate < currentTime &&
                               t.Status != "completed")
                    .Select(t => new {
                        t.Id,
                        t.DueDate,
                        OverdueDays = (currentTime - t.DueDate.Value) / (24 * 60 * 60.0)
                    })
                    .ToListAsync();

                var averageOverdueDays = overdueTasks.Any()
                    ? Math.Round(overdueTasks.Average(t => t.OverdueDays), 2)
                    : 0;

                // اتجاهات الإنجاز الأسبوعية (آخر 4 أسابيع)
                var weeklyCompletions = new List<object>();
                for (int i = 3; i >= 0; i--)
                {
                    var weekStart = currentTime - ((i + 1) * 7 * 24 * 60 * 60);
                    var weekEnd = currentTime - (i * 7 * 24 * 60 * 60);

                    var weekCompletions = await _context.Tasks
                        .Where(t => !t.IsDeleted &&
                                   t.CompletedAt.HasValue &&
                                   t.CompletedAt >= weekStart &&
                                   t.CompletedAt < weekEnd)
                        .CountAsync();

                    weeklyCompletions.Add(new {
                        Week = $"الأسبوع {4 - i}",
                        Completions = weekCompletions,
                        StartDate = weekStart,
                        EndDate = weekEnd
                    });
                }

                var result = new
                {
                    averageCompletionTime,
                    recentCompletions,
                    totalOverdueTasks = overdueTasks.Count,
                    averageOverdueDays,
                    weeklyCompletions,
                    totalCompletedTasks = completedTasks.Count
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب تحليلات الأداء الزمني",
                    "GetTimePerformanceAnalytics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تحليلات الأداء الزمني");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على تحليلات التعاون والمساهمة
        /// </summary>
        [HttpGet("collaboration-analytics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetCollaborationAnalytics()
        {
            try
            {
                // المساهمون النشطون في المهام
                var activeContributors = await _context.TaskAccessUsers
                    .Include(tau => tau.User)
                    .Include(tau => tau.Task)
                    .Where(tau => !tau.Task!.IsDeleted)
                    .GroupBy(tau => new {
                        tau.UserId,
                        UserName = tau.User!.Name ?? tau.User.Username ?? "غير محدد"
                    })
                    .Select(g => new {
                        g.Key.UserId,
                        g.Key.UserName,
                        TaskCount = g.Count(),
                        CompletedTasks = g.Count(tau => tau.Task!.Status == "completed"),
                        InProgressTasks = g.Count(tau => tau.Task!.Status == "in_progress")
                    })
                    .OrderByDescending(x => x.TaskCount)
                    .Take(10)
                    .ToListAsync();

                // مستوى التفاعل في المهام (التعليقات)
                var taskInteraction = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Select(t => new {
                        t.Id,
                        t.Title,
                        CommentsCount = t.TaskComments.Count,
                        ContributorsCount = t.TaskAccessUsers.Count,
                        Status = t.Status
                    })
                    .Where(t => t.CommentsCount > 0 || t.ContributorsCount > 1)
                    .OrderByDescending(t => t.CommentsCount + t.ContributorsCount)
                    .Take(10)
                    .ToListAsync();

                // إحصائيات التعاون العامة
                var totalTasks = await _context.Tasks.Where(t => !t.IsDeleted).CountAsync();
                var collaborationStats = new {
                    totalContributors = await _context.TaskAccessUsers
                        .Select(tau => tau.UserId)
                        .Distinct()
                        .CountAsync(),

                    averageContributorsPerTask = totalTasks > 0
                        ? Math.Round(await _context.TaskAccessUsers.CountAsync() / (double)totalTasks, 2)
                        : 0,

                    tasksWithMultipleContributors = await _context.Tasks
                        .Where(t => !t.IsDeleted && t.TaskAccessUsers.Count > 1)
                        .CountAsync(),

                    totalComments = await _context.TaskComments.CountAsync(),

                    averageCommentsPerTask = totalTasks > 0
                        ? Math.Round(await _context.TaskComments.CountAsync() / (double)totalTasks, 2)
                        : 0
                };

                var result = new {
                    activeContributors,
                    taskInteraction,
                    collaborationStats
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب تحليلات التعاون والمساهمة",
                    "GetCollaborationAnalytics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تحليلات التعاون");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على التحليلات التنبؤية ومؤشرات الإنذار المبكر
        /// </summary>
        [HttpGet("predictive-analytics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetPredictiveAnalytics()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var sevenDaysFromNow = currentTime + (7 * 24 * 60 * 60);
                var fourteenDaysFromNow = currentTime + (14 * 24 * 60 * 60);

                // المهام المعرضة لخطر التأخير (لها موعد نهائي خلال 7 أيام وتقدمها أقل من 50%)
                var tasksAtRisk = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.DueDate.HasValue &&
                               t.DueDate <= sevenDaysFromNow &&
                               t.DueDate > currentTime &&
                               t.Status != "completed" &&
                               t.CompletionPercentage < 50)
                    .Select(t => new {
                        t.Id,
                        t.Title,
                        t.DueDate,
                        t.CompletionPercentage,
                        t.Status,
                        DaysRemaining = (t.DueDate.Value - currentTime) / (24 * 60 * 60.0),
                        RiskLevel = t.CompletionPercentage < 20 ? "عالي" :
                                   t.CompletionPercentage < 35 ? "متوسط" : "منخفض"
                    })
                    .OrderBy(t => t.DueDate)
                    .Take(10)
                    .ToListAsync();

                // توقع المهام التي ستكتمل خلال الأسبوعين القادمين (بناءً على معدل التقدم)
                var tasksLikelyToComplete = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.Status == "in_progress" &&
                               t.CompletionPercentage >= 70)
                    .Select(t => new {
                        t.Id,
                        t.Title,
                        t.CompletionPercentage,
                        t.DueDate,
                        EstimatedCompletion = t.DueDate.HasValue ?
                            Math.Min(t.DueDate.Value, currentTime + ((100 - t.CompletionPercentage) * 24 * 60 * 60 / 10)) :
                            currentTime + ((100 - t.CompletionPercentage) * 24 * 60 * 60 / 10)
                    })
                    .Where(t => t.EstimatedCompletion <= fourteenDaysFromNow)
                    .OrderBy(t => t.EstimatedCompletion)
                    .Take(10)
                    .ToListAsync();

                // تحليل أنماط العمل - الأيام الأكثر إنتاجية
                var completedTasksWithDayOfWeek = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.CompletedAt.HasValue)
                    .Select(t => new {
                        CompletedAt = t.CompletedAt.Value,
                        DayOfWeek = ((int)DateTimeOffset.FromUnixTimeSeconds(t.CompletedAt.Value).DayOfWeek + 6) % 7 // تحويل الأحد إلى 6
                    })
                    .ToListAsync();

                var productivityByDay = completedTasksWithDayOfWeek
                    .GroupBy(t => t.DayOfWeek)
                    .Select(g => new {
                        DayName = g.Key switch {
                            0 => "الاثنين",
                            1 => "الثلاثاء",
                            2 => "الأربعاء",
                            3 => "الخميس",
                            4 => "الجمعة",
                            5 => "السبت",
                            6 => "الأحد",
                            _ => "غير محدد"
                        },
                        CompletedTasks = g.Count(),
                        Percentage = Math.Round((double)g.Count() / completedTasksWithDayOfWeek.Count * 100, 1)
                    })
                    .OrderByDescending(x => x.CompletedTasks)
                    .ToList();

                // مؤشرات الإنذار المبكر
                var earlyWarnings = new List<object>();

                // تحذير: مهام متأخرة كثيرة
                var overdueTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.DueDate.HasValue &&
                               t.DueDate < currentTime &&
                               t.Status != "completed")
                    .CountAsync();

                if (overdueTasks > 5)
                {
                    earlyWarnings.Add(new {
                        Type = "overdue_tasks",
                        Severity = overdueTasks > 15 ? "عالي" : "متوسط",
                        Message = $"يوجد {overdueTasks} مهمة متأخرة تحتاج متابعة فورية",
                        Count = overdueTasks
                    });
                }

                // تحذير: مهام بدون تقدم لفترة طويلة
                var stagnantTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.Status == "in_progress" &&
                               t.CompletionPercentage == 0)
                    .CountAsync();

                if (stagnantTasks > 3)
                {
                    earlyWarnings.Add(new {
                        Type = "stagnant_tasks",
                        Severity = "متوسط",
                        Message = $"يوجد {stagnantTasks} مهمة قيد التنفيذ بدون تقدم",
                        Count = stagnantTasks
                    });
                }

                var result = new {
                    tasksAtRisk,
                    tasksLikelyToComplete,
                    productivityByDay,
                    earlyWarnings,
                    summary = new {
                        totalTasksAtRisk = tasksAtRisk.Count,
                        highRiskTasks = tasksAtRisk.Count(t => t.RiskLevel == "عالي"),
                        tasksExpectedToComplete = tasksLikelyToComplete.Count,
                        mostProductiveDay = productivityByDay.FirstOrDefault()?.DayName ?? "غير محدد",
                        totalWarnings = earlyWarnings.Count
                    }
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب التحليلات التنبؤية ومؤشرات الإنذار المبكر",
                    "GetPredictiveAnalytics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على التحليلات التنبؤية");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على تحليلات أنواع المهام الشاملة
        /// </summary>
        [HttpGet("task-types-analytics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTaskTypesAnalytics()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إحصائيات أنواع المهام الأساسية
                var taskTypeStats = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Include(t => t.TaskType)
                    .GroupBy(t => new {
                        TypeId = t.TaskTypeId ?? 0,
                        TypeName = t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد",
                        TypeColor = t.TaskType != null ? t.TaskType.Color : "#808080",
                        TypeIcon = t.TaskType != null ? t.TaskType.Icon : "task"
                    })
                    .Select(g => new {
                        g.Key.TypeId,
                        g.Key.TypeName,
                        g.Key.TypeColor,
                        g.Key.TypeIcon,
                        TotalTasks = g.Count(),
                        CompletedTasks = g.Count(t => t.Status == "completed"),
                        InProgressTasks = g.Count(t => t.Status == "in_progress"),
                        PendingTasks = g.Count(t => t.Status == "pending"),
                        CancelledTasks = g.Count(t => t.Status == "cancelled"),
                        OverdueTasks = g.Count(t => t.DueDate.HasValue &&
                                              t.DueDate < currentTime &&
                                              t.Status != "completed"),
                        AverageCompletionPercentage = g.Average(t => t.CompletionPercentage)
                    })
                    .OrderByDescending(x => x.TotalTasks)
                    .ToListAsync();

                // حساب معدلات الإنجاز لكل نوع
                var taskTypePerformance = taskTypeStats.Select(stat => new {
                    stat.TypeId,
                    stat.TypeName,
                    stat.TypeColor,
                    stat.TypeIcon,
                    stat.TotalTasks,
                    stat.CompletedTasks,
                    stat.InProgressTasks,
                    stat.PendingTasks,
                    stat.CancelledTasks,
                    stat.OverdueTasks,
                    CompletionRate = stat.TotalTasks > 0 ?
                        Math.Round((double)stat.CompletedTasks / stat.TotalTasks * 100, 2) : 0,
                    AverageCompletionPercentage = Math.Round(stat.AverageCompletionPercentage, 2),
                    EfficiencyScore = stat.TotalTasks > 0 ?
                        Math.Round(CalculateAdvancedEfficiencyScore(
                            stat.CompletedTasks,
                            stat.TotalTasks,
                            stat.OverdueTasks,
                            stat.AverageCompletionPercentage), 2) : 0
                }).ToList();

                // أنواع المهام الأكثر استخداماً
                var mostUsedTypes = taskTypePerformance
                    .OrderByDescending(x => x.TotalTasks)
                    .Take(5)
                    .ToList();

                // أنواع المهام الأكثر كفاءة
                var mostEfficientTypes = taskTypePerformance
                    .Where(x => x.TotalTasks >= 3) // فقط الأنواع التي لديها 3 مهام على الأقل
                    .OrderByDescending(x => x.EfficiencyScore)
                    .Take(5)
                    .ToList();

                // توزيع الأولويات حسب نوع المهمة
                var priorityDistribution = await _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Include(t => t.TaskType)
                    .GroupBy(t => new {
                        TypeName = t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد",
                        Priority = t.Priority
                    })
                    .Select(g => new {
                        g.Key.TypeName,
                        g.Key.Priority,
                        Count = g.Count()
                    })
                    .ToListAsync();

                var result = new {
                    taskTypePerformance,
                    mostUsedTypes,
                    mostEfficientTypes,
                    priorityDistribution,
                    summary = new {
                        totalTypes = taskTypeStats.Count,
                        totalTasks = taskTypeStats.Sum(x => x.TotalTasks),
                        averageTasksPerType = taskTypeStats.Count > 0 ?
                            Math.Round((double)taskTypeStats.Sum(x => x.TotalTasks) / taskTypeStats.Count, 2) : 0,
                        bestPerformingType = mostEfficientTypes.FirstOrDefault()?.TypeName ?? "غير محدد",
                        mostUsedType = mostUsedTypes.FirstOrDefault()?.TypeName ?? "غير محدد"
                    }
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب تحليلات أنواع المهام",
                    "GetTaskTypesAnalytics API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تحليلات أنواع المهام");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على اتجاهات أنواع المهام عبر الوقت
        /// </summary>
        [HttpGet("task-types-trends")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTaskTypesTrends()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var thirtyDaysAgo = currentTime - (30 * 24 * 60 * 60);
                var sevenDaysAgo = currentTime - (7 * 24 * 60 * 60);

                // اتجاهات الإنشاء حسب نوع المهمة (آخر 30 يوم)
                var creationTrends = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.CreatedAt >= thirtyDaysAgo)
                    .Include(t => t.TaskType)
                    .GroupBy(t => new {
                        TypeName = t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد",
                        Week = (t.CreatedAt - thirtyDaysAgo) / (7 * 24 * 60 * 60) // تقسيم إلى أسابيع
                    })
                    .Select(g => new {
                        g.Key.TypeName,
                        Week = (int)g.Key.Week + 1,
                        TasksCreated = g.Count()
                    })
                    .OrderBy(x => x.Week)
                    .ToListAsync();

                // اتجاهات الإنجاز حسب نوع المهمة (آخر 30 يوم)
                var completionTrends = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.CompletedAt.HasValue &&
                               t.CompletedAt >= thirtyDaysAgo)
                    .Include(t => t.TaskType)
                    .GroupBy(t => new {
                        TypeName = t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد",
                        Week = (t.CompletedAt.Value - thirtyDaysAgo) / (7 * 24 * 60 * 60)
                    })
                    .Select(g => new {
                        g.Key.TypeName,
                        Week = (int)g.Key.Week + 1,
                        TasksCompleted = g.Count()
                    })
                    .OrderBy(x => x.Week)
                    .ToListAsync();

                // متوسط وقت الإنجاز لكل نوع مهمة
                var completionTimeByType = await _context.Tasks
                    .Where(t => !t.IsDeleted &&
                               t.CompletedAt.HasValue &&
                               t.CreatedAt > 0)
                    .Include(t => t.TaskType)
                    .GroupBy(t => t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد")
                    .Select(g => new {
                        TypeName = g.Key,
                        AverageCompletionDays = g.Average(t => (t.CompletedAt.Value - t.CreatedAt) / (24 * 60 * 60.0)),
                        TaskCount = g.Count(),
                        FastestCompletion = g.Min(t => (t.CompletedAt.Value - t.CreatedAt) / (24 * 60 * 60.0)),
                        SlowestCompletion = g.Max(t => (t.CompletedAt.Value - t.CreatedAt) / (24 * 60 * 60.0))
                    })
                    .OrderBy(x => x.AverageCompletionDays)
                    .ToListAsync();

                // الأنواع الأكثر نمواً (مقارنة آخر 7 أيام مع الأسبوع السابق)
                var recentTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.CreatedAt >= (currentTime - 14 * 24 * 60 * 60))
                    .Include(t => t.TaskType)
                    .Select(t => new {
                        TypeName = t.TaskType != null ? t.TaskType.Name : "بدون نوع محدد",
                        IsRecent = t.CreatedAt >= sevenDaysAgo
                    })
                    .ToListAsync();

                var growthAnalysis = recentTasks
                    .GroupBy(t => t.TypeName)
                    .Select(g => new {
                        TypeName = g.Key,
                        RecentWeekCount = g.Count(t => t.IsRecent),
                        PreviousWeekCount = g.Count(t => !t.IsRecent),
                        GrowthRate = g.Count(t => !t.IsRecent) > 0 ?
                            Math.Round(((double)g.Count(t => t.IsRecent) - g.Count(t => !t.IsRecent)) /
                                      g.Count(t => !t.IsRecent) * 100, 2) : 0
                    })
                    .Where(x => x.RecentWeekCount > 0 || x.PreviousWeekCount > 0)
                    .OrderByDescending(x => x.GrowthRate)
                    .ToList();

                var result = new {
                    creationTrends,
                    completionTrends,
                    completionTimeByType,
                    growthAnalysis,
                    summary = new {
                        totalTypesTracked = completionTimeByType.Count,
                        fastestType = completionTimeByType.FirstOrDefault()?.TypeName ?? "غير محدد",
                        fastestCompletionTime = completionTimeByType.FirstOrDefault()?.AverageCompletionDays ?? 0,
                        growingTypes = growthAnalysis.Count(x => x.GrowthRate > 0),
                        decliningTypes = growthAnalysis.Count(x => x.GrowthRate < 0)
                    }
                };

                await _loggingService.LogSystemEventAsync(
                    "DASHBOARD",
                    "INFO",
                    "تم جلب اتجاهات أنواع المهام",
                    "GetTaskTypesTrends API called successfully"
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على اتجاهات أنواع المهام");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// حساب نقاط الكفاءة المتقدمة لنوع المهمة
        /// </summary>
        /// <param name="completedTasks">عدد المهام المكتملة</param>
        /// <param name="totalTasks">إجمالي المهام</param>
        /// <param name="overdueTasks">عدد المهام المتأخرة</param>
        /// <param name="averageCompletionPercentage">متوسط نسبة الإنجاز</param>
        /// <returns>نقاط الكفاءة من 0 إلى 100</returns>
        private static double CalculateAdvancedEfficiencyScore(
            int completedTasks,
            int totalTasks,
            int overdueTasks,
            double averageCompletionPercentage)
        {
            if (totalTasks == 0) return 0;

            // 1. معدل الإنجاز الكامل (40% من النقاط)
            double completionRate = (double)completedTasks / totalTasks;
            double completionScore = completionRate * 40;

            // 2. متوسط التقدم العام (30% من النقاط)
            double progressScore = (averageCompletionPercentage / 100) * 30;

            // 3. معامل الجودة - عدم التأخير (20% من النقاط)
            double overdueRate = (double)overdueTasks / totalTasks;
            double qualityScore = Math.Max(0, (1 - overdueRate)) * 20;

            // 4. معامل الاستقرار - حجم العينة (10% من النقاط)
            // كلما زاد عدد المهام، زادت موثوقية النتيجة
            double stabilityScore = Math.Min(10, totalTasks / 2.0); // حد أقصى 10 نقاط عند 20 مهمة أو أكثر

            // الحساب النهائي
            double finalScore = completionScore + progressScore + qualityScore + stabilityScore;

            return Math.Min(100, finalScore); // الحد الأقصى 100
        }
    }
}
