{"GlobalPropertiesHash": "O6KaarPdnB16ZWliZnCuyT1iyLTy/b7CJpeLN7h2+2k=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vzQc9zSQGtQdcK56lk3gB+cYr7FOG15r1ihnrBO5t/8=", "ooBnhxGi10LFVMPpDDIx03fwA7M7JakQgH54tfrjTJg=", "3IB7Z21GrhNsIjdkwMJr7NKoUOyp90GcAPH23Ej+2gw=", "95jK69G79y7AMDPVzxbMWO2aSMLopn+1lA+SZQn0dgs=", "PvXaOhf3hVqbJBzL+GqmnAKh2N0FfbjiAin0mTtNxzo=", "TZldKv93t6jn0l2D6EZd4xLtCbIdtJZk9XhwAXpnd9U=", "RaIcroESbNkD8f0LGhB5whyi0fihnl0tHw+i7qKJu+U=", "A4eM21/w2643SrZY8d+eJzDbEh0FMEikSRlTMcmCTJE=", "AHeTWPR8dJ6nv8N8dhlGQvIQ249n3oSKfVHt9g8Zdb4=", "eDgCF+bPyHOBww/hk223ZrDOVO1VZuOYS9LwNGOv22M=", "87TkO/5nE/gkLE4uziZ4uFHYH2qdzoAjFEMhFYQqe8Y=", "NlqFSLl4PFtMXkQy1m2Wrcak/98lRCsUJNw9J6KJJF4=", "l1wohJhVbtRudLATTCRBRIhtTOtNl9et/Z1P2BlpXn4=", "4fvUkKswEVQFQLURgW/yyaKj0/yzVYhKs6e0PTyy3uw=", "jOE5pHWWJ06pMtXWvw0BAFYPDB0/89LIPFgG4prvZ0o=", "Q//Qi9ZWJRHNm3I/nmOju9+80/Bb4FWE5i09AMzlnFU=", "/URHy7MX9EIRI9sswyC0PElAncBXzJI5Yu16b2AhDCI=", "vdtvlP6ycHrDmkoWICMBSSi0fxAjYfobRQKAXYXVCms=", "Ml3n8qaIXveAknd4f0GfLmyGR/yk8JIIb7zpaYtf4d4=", "Yy2samsRT2uCAY2lIlRJDc0N62L8xi+tW3CSIHmbg1s=", "73g1dKxPGSb1OJMktTB0PzYi+vkOvnVsSeMQVhxNc8A=", "pGJuRlkE0S4/1UOOx95bB/KMKh3qsr5sG4ytnYcuH7E=", "CRtg3bPeLxqmkAdBps1UGO3ARi0GiaZg3dytRu4CIiE=", "jsLUuEwL3J8UpqQsLTPWhJAWZ5yfhc7ytU1FtAa6d2w=", "Eym7jLdgzEMo/RqZFfKhhpseuwdC8kB4Y+ofIvBchY0=", "pSXAVYrPfaDUrjb0KGf4WjuvHOlm7MQUXmm5TH++HpY=", "1vUXHSxtQFvvqaL9PVXhPgpd1GZsOczeo6fR+isr8VE=", "sdFF0u75fJYUQO7mXvTnmgIAuCDi7T4JUxaMMbY0dhM=", "DGYMZC1R13mCyae5KF4LKMHD2ppmyUnXgESa78Z2oIo=", "M1QZj0xgV+NRgFmvxxJ4rLCZeyV3oG7Wnb0sF4Hbjj0=", "7d3hJHDHm5FsOJSFI12ukVvqOoXCEw01mgDFbk092j8=", "1iAipClIP/28z2b4LmT7fZq3IvWHoUjbabl5UmOjaEM=", "iO690+TBbeCEcsgGRMti30rZUuQrApLbDPrYXOFXBFI=", "svNer+x1BBWuxorInGjNKIoGxwrmttAyL9jC3FElFgw=", "Nw545vydZ0NpWFzool/ptfMXvJMWexQPtkKDuomHjbE=", "fqUVTZvtJ7ZMHZl+gfXGOfX3cVCuF/hSocC4JY0Ksx0=", "+ifrVhPVyeYnbiXasePNbh2VGPmv/Nat2qQD55xTsN4=", "pvNzvuRCg5iueyZl7eT61GycZuGiZk2yWlXsIyx/qEM=", "tdmd1AzMR+MZu23+awGL2HYcduMvTfS2CsdDwVAz/mU=", "RMlEKN4Abqubsag3hywTE7X7DY1qUbaQykVKIHaYdLI=", "0VZIjBMkI3NlpZ9ouS0BmtKqEjHDM8w1gT+GUl+HshY=", "5yyGiDESKl7yeKkm12Rq9oQr1IN6f69x4INbs/B6uHI=", "5kPPyKxXh9LMCV0m72x9a04XJXJnOsUwGv4IGscl2IA=", "GsUF/Bk1Q5dBMBXfXFQQc9xM1NKhOzHmNyGr1pp2lQA=", "XDlxoN4+PDdfhvzRc+swxI//xTnw/MdeH4kHYHo3Vjc=", "pDbUAGMCV3tyF8bY6YbrbDMC/YZGH6yk3WeDmXV1vYk=", "1X1NFxZi5t7ZPMUpsPIrJn33h4mrO2hhfcgpSNcQpwo=", "FBrkA5sfHgqONe9JIBZ6nfOZX+qf1L6E5Uxpt7FdcXM=", "UAO84WjNXPNrtessSME8hh7/b0oRI1g9jC7xF6HvyKY=", "Iq8OCo1q4ah3IZwViwNuO6MTiI0lMNdY54pCtRO7358=", "rfEs9jcBXXQmciRsA3V59y0YOGzQ6GdNYOtmLnT2cfU=", "ncbu82uc8MJ/PzD8QOQmFqr3/4HnAp0kCcjrAR1zyfk=", "7vpP8tKIsabGKIYd5NeqNACv5vUrai5IiQ4b5Eg3O7A=", "p4iPilNza/T/FYIBQkifDQPGG8ERdm6MYoAuAxGW+YY=", "GRyKd2qxDLLEWKW9CGuCWCBcjYGgUXPDuTEvTjrM4Y4=", "iQAhAIzxvAqngWtUOv8u6XW9RmWm6phHiIie04LrPLM=", "Zna7SwJDBUHWuv3PzxLWIfOkaQyOQ9IVvXfM0Vm028w=", "wMQEqglctd2NqfBhltuUiIIym/MWPKI/FWuHb36XKBM=", "1obPk6u8AhaPYoU1qQIKtuPlWtXd196oZT3mARJPOVA=", "a2p6Na5B7IKLife1PGBoh+SpaBLaW/iUI66bPNBDAsc=", "dO+67+xCd1I8LnXU0l6L7kJkLEdUmwD/YegGMrY8OZI=", "vS3/q/t36ExG22y4DTamXh51FkWZrIfnifxr9Us8pJo=", "9mCpvJluBojvSCFt4ks2EartcsINoVd9faaHEoR8POY=", "0yV4o6YSCCEP70X+Hn3yDEhn97qY3qVV7FvqspinIwY=", "kOOFfpUdKR36Yq5VtNQ4lfq3dSV7tVipvbyFrkGHPW8=", "CO7x5DSbsIuowarMLwIKA1uILeQZ7i79jC1Gz3f2Y8o=", "puHsqH7urbW3xZUAbBO6zqgfuxdOa8bbs6boMzRxmxk=", "+FkH8Duj3p8lL9bLZ9NDYQDQzJhR+2v8XxHm3qqubFU=", "hb1p5R2rPvcoh8DFJHfHFVGQE0HlpAMrJ5UGmbopGQk=", "zHL9cihnagFLK3h99yjtb4I2wKBuZsj2obTWtC9qV0o=", "h81dwrVRNaKjkXzSy5lsI+ur7/Q/Y2wHM+mfx53tWAA=", "x3q8WKiRJ/nEvo5ekSarmerIHYg5GohsZ8L+aikIb9c=", "hkXlxku51MH5sjL1y+8xOT5IUABVOX09xnCOQjiyZAI=", "6jCipdlU3ZTIiwUSMbo+SDc+IkpFH41EZGecCKKaD/k=", "PlO2aWxNFcYRCwV2hBr4lfTPj+U6saYfV/91bRqgJa8=", "otfX7cbfzrCjHrHBYEn2aOhmtKBOKa6Z3pqmQ7ysOdE=", "kH4mMZL2anSpCg3HI++u+gWcO3bB0kPxAf0hNHzOYVA=", "ih+IqZEshrO6IoU5NLuu6BPwd+r0ZAGjhsUf61h7054=", "lMI3ZdMzHr29GHk5Pj07aGNtVg8AiPOBnbB8IaKey6E=", "u2E6twIKUPxyUC5u3A97tGpI428fauFVZ1jqOQDykRI=", "UGBkA4yg2tNm9HBSGpOOS2zUmp+X6r5evvjUC6YU2Es=", "R2tn7qbd9zAGmggeT7J6H4+vLmj3z7DmOih0bJ2/vWE=", "X7eRFM8JMXMepr7c3vsNDyNFRc4VUsZl9CHGtXamtsk=", "99uB0UXlLbf3KX3jecS3NvXWWGysZKAQZYzy3rgBlxU=", "EI7VMakM5LQeUx8uNkrgMrsr/DQiIT35BWRnEjhdCXU=", "AvF6iYvDaSAEGH1VM4Cteb+8RBJPKNTjmH+RJS6xspE=", "vmB6DCPoPXgcQ7HjD3+G+zdf0bYUUp93Bk+QLHaLpPs=", "Kk6xLmSbYOvZrzrY3W9nKxm8nwW6LhJ+uM11QpwwoYc=", "Lb+6fEOlPebg2yOwPld1J0RoC/23s8wVTHRtGkAyvCY=", "zoFHB1rpsjcX7obPGe169yn1DMpIdyqL9cqTe8JKh2o=", "Y/6XpapjKKQfnG1LsXrHYrR9zPGz+I9kc+GYlXWJTyY=", "Rv3nc5WR8gQhsPpo+r17l81kQa8qNnhVkvE35k80et0=", "fvctIhDgvr8ypf2Mj1VYGBqT1YjLU5LXUAsSfDhUn8g=", "d7JwoWNV5PIq3QtEDoqWGz1ccSzxyC70hn/XknsX4pA=", "Z+yQYGf3wt2X9yIh3dvfOvFZdYE9SAJITqN+5ry+ZS8=", "AUzcoW38qLtvouVuHTuXGRccYqR8ExOL/sqH+Ari9CY=", "XWlEfuoKJl/E3LYQkVS6TtnECofnv5yig9evLoY7L9c=", "L10HvpWA13+JD3NP9fyHGEQ0nq+hFUUaLukC50T0hyY=", "XlDwoX53eK3FJxk6A+sSV6c5pDSa7jwvIvoJP58Kuac=", "juvXZX+UmY0KLvdDX3TTMKPSiZmFqx2Pvp0biwENCj8=", "GABg1ejfzTU/1yMFjd0PdiOi0pB1l+k6v8DFODhTITI=", "5IfGJz8CtH7yumXTFISPfvjmE66xw2OPXqhoepQ1SBg=", "F/RR0qGobGLAywrn+qtBawbsl4hqYWNW8zHIB58IGbk=", "Yr4i0RiT4pe3jrG6x0mxOHrzCOxO/udRab7gHPKQ3N4=", "asqLdl7X4DRU0tCEJ4KJ9w9CsGsk8oNHmyt/GGQ254Y=", "EDIbMb+QFamsOss6QvWtPcVIWWOa3xwMEWPGjGgXVNw=", "Y4ZJ0nolwkiVL8jnUmI2hKZ89B6Ix1YLQMwb8r/wOpY=", "yJN0fbGpny7pmUP+Ivu7fccYBjmGidrvoMJObpqv9fo=", "bRhfMefJB2wClV4abP+VXtYNn5D7o0wTlhCl3GGWMVw=", "ecDhX6KBdU/4qNBdeuOpFledsh7LO9tc1wiEllAIsu8=", "vVV1s4pLY8uLaIc0Ka25cqMtW6wZCYMHAUKm1zj71SY=", "T5UjcNb3dTVVQgYgQxG92SvxORkLh5g1JZoUXU5YdR4=", "j37Xdl7ErCIy7pYsg7GouZXZlwfagKMreWdQlZixGEE=", "tvEsZBs/+VmnZ+XVGSgxxaBbwE09qigMff3rkNcNtcU=", "zVH9cPLC3XKvkYsdKFUYf/nd9ULh2JxFUBTPspe6p9c=", "JCmTzhRRb+TZ4uiNnIcR3Rliwubcy8wbcitxNv4F4mk=", "J0fRpSANXZkwdXtKVkXamW9DF2BYhVGiBri9KpfIuGQ=", "2OWggBV6Mygwf56er0WDupa9Fwo4GxRWHtwJlB64uvg=", "CHK6tvJVUCrFoNWd7UfOFpQEnbgiMr3V0aD0oW39LOQ=", "a3AafGcgN3bWTTV2xwXgHAF5ud6gkPiFwzX7Rr9cDkA=", "Oof8jYzNsBP/6IRadLyOlhnszUrQxj/s0zdbtnLrqZY=", "gMr3EvWO+hEgwLgOYXJQjVoLNRCyB1iPpiC9ZO2Bjcw=", "VT0g1dGviXEEvX/+70CQM3TKQiWJQ/W9SqVA+jrlCb4=", "bL4VMjLuTtVRegYxotlNQo6SWNPwhOzkZxe4udpLz/I=", "E9v9vaTbWKw28k93W6V/ElVxtIOWsb8CaBlJS+f3764=", "jYXOk6JJW3c1XdyeFatgKNsrEbD2BS6qWYiT9dkkVlo=", "/NviJnZhIWy91npIwMRK0hV2nUG2dhQfNaT5jjRS33s=", "Qwhb9tTgefx9QisYkGbwbT1g4Ur6bCSeh4PurzekzO0=", "b/2RHfz/l0qPm9dy0k6h6pvz/6Q13IHSK0DSPldaL7w=", "JJtHTNWlYiIdeh+M/VnMIr+E8nZMEkXQFvPdIzI9ji0=", "a1NuCd0fz74cGl/oyR59DHuUZ4+vff/V9r78bTRnl94=", "kJQVtesQAb/dJ/Z/ex5k9pHyBFWF4SCD8RuEyTZe9G4=", "EqFU2HdeilVIcSNpMzvvpTrnD+U6/5M+ONn//p2LyrU=", "Idt+PjHtkt9181zCl+O4T6862sTePRk0NdfGUXPyal4=", "yoWmh6/mm6W52D1I977S8t9+cETkMeqP2AY5Jks2h2I=", "cm5uZ6LFmzJjDrTXQZEGNh29JDlmNvILl4tVHubOhk4=", "VoJ8wI8MbKS6UOwJLVoc13zbeHR6vNxhVe2xfv3FZyE=", "GrcgsfyxFbChYD3iQZWINLc3jl6tQ7BSUjoQzY9nleM=", "d+9Ft/q/YOYO95IGKopPVOV534qQ8BbA6lf3dxHAsgw=", "tCj5tUJF6sqatVrCjIMOE3i/pWs2JTPgb8K/8T3/lO0=", "367ZOCts2LdMVV7T5dRnu1gItmtInxebqPPOxjzv9Kc=", "uMZzCLl5PNBI3IFol7XRckr3tgT+zf3Hfyfe7sKmCvw=", "67y2oSjwGZGO/iBVPTbaWpqRFrN7OgnkepJBXy4YzvU=", "i2uPQHlwXbfCEeTF0iCAfDud0dGPFgm/xRbYPIrti9s=", "gI2IN9t0gV6LE80Fgy+E3mPj/gj88MSaBfyd9Ptj9S4=", "BK0EirozkIIh8MZb/VNwC3sF+KiQSGCR3BA7NMY9bGs=", "YxdwYXKeXtx0SGTfBsuQm2CqGoxYR4DmMj71x5WUEYE=", "JPVFUTJR1b8R52//81vSobTzaOWPoWPxt1v7CUY3PY8=", "62eNWqZw0OrdoyXGhXNQEXXnzFJ5G34B40ABhBefk/A=", "znPKdJzyAyfiLuyKCzs45c2lrbhTqdb7/UYjSkY1NpU=", "b/vbwz5HEsGKHTj7h2EUKtm267GXqH38qLQEjLL+aEQ=", "IHFi2VxxRGBkatfE7LLKSU7iasrckHcDIRmvmqm8uD4=", "LdGf57kDvRWu+h/jUD6EXdaEsMDUw/sQyILfJnpc2fU=", "PMYezIFaDPAhIbqOjZeljV/ilqoJW8+jJmQ2BjRpwnM=", "JyEBTtQOULVIdMkOGBgS9X2+TUvlYpCJNgrHIdUmuxk=", "sJ+eEaC94oNrX8vBg9TqeNPAkli/FGUcqTh5CDDKmSU=", "u/lXJosLJbgN2KDwihvy21YjKX2GLVF+ty7N4WsQ9F0=", "jUSpJAw5KgISTBeHyKHG74n22pvqA0R4QSQPtYUPWzM=", "GKMLw9ZvouAssKt4Y4hk3Jn5pMkJOqHCt5P23rZ+YPU=", "/aCw6FvtqTZFrbKyAAln9nyILgLoPhpwKtOfJ0nXJhc=", "T7fftO/8ZjwwI7JT4s1Z9iMEq4NQ0gW69W65HuX0bSA=", "OowhQNgvfN9sQw0dJrTAs7a7PA0Hh4g9ApPUNYQXQF4=", "nDCpVwkq3dqdtKKE9SQ0oJYgxGAiiRcDAqKzSVWm/Pw=", "/t8NOfsk9V77CKXJ/iBNFRp+QprUTYtLIKRMMuIout4=", "kKe1Le6DVLmUxlZewmABFqdxl4LveWFoyzKNV9qHXDo=", "ZZEOjH/4wdcbf5rpTZ1XDC3m/2t521DAQx4vUbiVwME=", "YNfnAaNKV7BXERvWioMZ6rKN7eRiVnZleR3Zozw6cKU=", "yK9AEsf3zKS3jNygseVBh7zq8SyXk+ZDGc43zin14CA=", "kZnfe2f32eKJAzNWFPOpAwZ6T+Hx5mxZov451Kfo9tQ=", "AwIRwycqEYKfzwC1EPZ67iYGrgJuvCU94v4qIPgcpZk=", "eVd5Olhicxk7UvQ0zgE/I5PyGApyhLX6sXtqaiQaYQE=", "81qaPfAJ2rfkytC1JUH7IcCRUZTSb5RIyfDPj3gmw1Q=", "CPbGMnS9GcZQ9YrwDiQ+yr+shEY2P9oLOfx96X5940w=", "eSWvwjGILUMVLeQrl8wA+IxNIUD65WTKsn2pHnOZlSI=", "Xl7trBvM06iX/0MblmaVedfoppgOVcifNbSyYucD5ek=", "pFGCkA0uD4N59pZHnPMM7Xi/5Mx9miUISt+Yv6zIvAc=", "XQbQWwDHA4oygDU7UbiDhv5H16YXqXk8mScs606R5bY=", "A91MheFX895dWCb6A45EJFqqF9Rz8Gw0OOauSxXqmow=", "x9Q2P+DAETPjJqk4N9ivLi3DsDfqccaKjgFKbYr/eaw=", "27X7krisLuwkH/NdZIxPtCgWrKqjnVnamffZGIZaXd0=", "jF+eQrQnwg+X+Kg0yWZ74DfpUuvK3hIvLp2P54queTQ=", "U3QicamCG19Ow/9TzCW043RC6JW5FM7Ts35jmNwF5m8=", "IDH7QaQ6VaxvXaY4fZUOiIXXQBnSPpbSb42MMo/54eA=", "vIKQpIPeO3uUp+Sy0ri7LSzzEuK8uFtdl2rGZkFR0Ss=", "W6AFCz/o9z9mf5YszHpsOAd3RIqcSHIXtTMbeA8Erv8=", "nI08cSxAs1ywX8gHwfly40SNg1C3hEHRHi8ji+22agE=", "QToCUuKCUOZXx/usfyTwyiblQfjRXrpbmbo+qEHIzr4=", "i0wxnXgQXTJFc7olgTdzb8xa3PyENHQ4zqeqa23dUfQ=", "4W0Bys+H3E25NZVqLyhwv3UBsUnHt6QiZSQ59DRIQ6w=", "XZMRm/mYa+DsC5TKa3lu1koc33m518s8FuyBeEtb8U8=", "+pC+7f1nyDzDKnCtdvXKj8TfZU4vTnjH+Hj9MWGIHwQ=", "G+bOWz5GvCh0TB+gggW6ytCYJSpkZVxPiS67SjLEB+k=", "SnnfihuzjHB0ILXPWs5uPryAWNsLm/ssbQuxmazBitA=", "X/K8mf399zmBF5d0yXvcGzpcrBEtB9inSrx3z7TWX9M=", "OiCl6m56f9leifefFgjyJ1RcL8bL6iM4b1hxJg5nJhg=", "lSf/PdL/BsbyAzctI9Ex1MWWVFctrelE/d+aah8Fp/s=", "/MqwXMnbgz4Cvd26VeC2m5klWbeyhpJ1cUSJ1lGL9UM=", "Onz1nJuHmU3bHLYlkQCuIp34eAGljoKlkaliJqhHCmI=", "aX5HBs3PBQfNvPB5oGfCdCKIEyUD8t1O8oYBjzlv1Cs=", "WbqGz0xnGXDfb2X16ryBx0nldQQrykxkRoncWnooL4w=", "m5El2tfIxJ+wdpzzlOL8JyL4bS10uXoBho1EvHfr4BU=", "0bFkU9Q01qU0G+cruKLeHMPzk7+GG/WyAQ2DQDIBmuE="], "CachedAssets": {"vzQc9zSQGtQdcK56lk3gB+cYr7FOG15r1ihnrBO5t/8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250707_153306_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250707_153306_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3nitcostfa", "Integrity": "YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250707_153306_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-07T15:33:06.244357+00:00"}, "ooBnhxGi10LFVMPpDDIx03fwA7M7JakQgH54tfrjTJg=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250712_033808_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250712_033808_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "18we1jq8q4", "Integrity": "oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250712_033808_0.sql", "FileLength": 35742208, "LastWriteTime": "2025-07-12T03:38:08.3365517+00:00"}, "3IB7Z21GrhNsIjdkwMJr7NKoUOyp90GcAPH23Ej+2gw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_141356_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_141356_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wei1ctf611", "Integrity": "iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_141356_0.sql", "FileLength": 64536064, "LastWriteTime": "2025-07-15T14:44:00.2797825+00:00"}, "95jK69G79y7AMDPVzxbMWO2aSMLopn+1lA+SZQn0dgs=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144541_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_144541_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u0kw9kgy4w", "Integrity": "otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_144541_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T14:45:41.7754361+00:00"}, "PvXaOhf3hVqbJBzL+GqmnAKh2N0FfbjiAin0mTtNxzo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144735_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_144735_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "olba8xqros", "Integrity": "POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_144735_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T14:47:35.9136493+00:00"}, "TZldKv93t6jn0l2D6EZd4xLtCbIdtJZk9XhwAXpnd9U=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150009_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_150009_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zy0q7wnq3f", "Integrity": "a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_150009_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:00:09.6023663+00:00"}, "RaIcroESbNkD8f0LGhB5whyi0fihnl0tHw+i7qKJu+U=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150334_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_150334_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jwjdiclzr9", "Integrity": "A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_150334_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:03:34.4869844+00:00"}, "A4eM21/w2643SrZY8d+eJzDbEh0FMEikSRlTMcmCTJE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151225_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_151225_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d77ue2n4io", "Integrity": "B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_151225_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:12:25.2695819+00:00"}, "AHeTWPR8dJ6nv8N8dhlGQvIQ249n3oSKfVHt9g8Zdb4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151229_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_151229_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mq6ep6mvf6", "Integrity": "YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_151229_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:12:29.3621019+00:00"}, "eDgCF+bPyHOBww/hk223ZrDOVO1VZuOYS9LwNGOv22M=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153548_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153548_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hxng2sfg3k", "Integrity": "Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153548_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:35:48.2667873+00:00"}, "87TkO/5nE/gkLE4uziZ4uFHYH2qdzoAjFEMhFYQqe8Y=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153556_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153556_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wyz7qo4ryg", "Integrity": "2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153556_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:35:56.300123+00:00"}, "NlqFSLl4PFtMXkQy1m2Wrcak/98lRCsUJNw9J6KJJF4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153621_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153621_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yosmphs06q", "Integrity": "PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153621_0.sql", "FileLength": 107354624, "LastWriteTime": "2025-07-20T19:50:18.9598425+00:00"}, "4fvUkKswEVQFQLURgW/yyaKj0/yzVYhKs6e0PTyy3uw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\9fdf591f830b467d9e9ac4c125131b83.rar", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/archives/9fdf591f830b467d9e9ac4c125131b83#[.{fingerprint}]?.rar", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "osus9zaa8z", "Integrity": "6L8YQqYi28ANVpCpyVUo2WBDILZOK1qRwzDPfs1RzHY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\archives\\9fdf591f830b467d9e9ac4c125131b83.rar", "FileLength": 1073505198, "LastWriteTime": "2025-07-31T21:27:31.7427619+00:00"}, "jOE5pHWWJ06pMtXWvw0BAFYPDB0/89LIPFgG4prvZ0o=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/archives/be70474c4b4744bc9edf86d5b08c891e#[.{fingerprint}]?.rar", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6j92<PERSON><PERSON><PERSON><PERSON>", "Integrity": "Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "FileLength": 16312166, "LastWriteTime": "2025-07-23T17:42:09.459011+00:00"}, "Q//Qi9ZWJRHNm3I/nmOju9+80/Bb4FWE5i09AMzlnFU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\f633e710c44f4ff4933199e4741ee8a3.rar", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/archives/f633e710c44f4ff4933199e4741ee8a3#[.{fingerprint}]?.rar", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m9zrhb4era", "Integrity": "tpf8oVBym3vbGGd0NDsdPcjmRyjiD2yqz8w7rYI/UqA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\archives\\f633e710c44f4ff4933199e4741ee8a3.rar", "FileLength": 357835104, "LastWriteTime": "2025-07-31T21:23:17.5418582+00:00"}, "/URHy7MX9EIRI9sswyC0PElAncBXzJI5Yu16b2AhDCI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "FileLength": 97050, "LastWriteTime": "2025-06-22T15:42:44.8901215+00:00"}, "vdtvlP6ycHrDmkoWICMBSSi0fxAjYfobRQKAXYXVCms=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-20T19:14:11.841301+00:00"}, "Ml3n8qaIXveAknd4f0GfLmyGR/yk8JIIb7zpaYtf4d4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q5n9oh9rw1", "Integrity": "gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "FileLength": 1091955, "LastWriteTime": "2025-06-20T21:20:26.99614+00:00"}, "Yy2samsRT2uCAY2lIlRJDc0N62L8xi+tW3CSIHmbg1s=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dz1rjozczi", "Integrity": "NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 28469, "LastWriteTime": "2025-06-20T19:16:26.6311732+00:00"}, "73g1dKxPGSb1OJMktTB0PzYi+vkOvnVsSeMQVhxNc8A=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "je3vbe9eb6", "Integrity": "+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 24217, "LastWriteTime": "2025-07-23T21:56:34.4796625+00:00"}, "pGJuRlkE0S4/1UOOx95bB/KMKh3qsr5sG4ytnYcuH7E=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ecem5x2v5n", "Integrity": "4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 3535, "LastWriteTime": "2025-07-19T19:08:43.2774233+00:00"}, "CRtg3bPeLxqmkAdBps1UGO3ARi0GiaZg3dytRu4CIiE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ecem5x2v5n", "Integrity": "4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 3535, "LastWriteTime": "2025-07-19T19:11:14.7705855+00:00"}, "jsLUuEwL3J8UpqQsLTPWhJAWZ5yfhc7ytU1FtAa6d2w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/02cc06431c17416889e85b70a05142e2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:20:30.6434613+00:00"}, "Eym7jLdgzEMo/RqZFfKhhpseuwdC8kB4Y+ofIvBchY0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/052eb05336464289832f7814ce2d7630#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-11T22:16:28.1536033+00:00"}, "pSXAVYrPfaDUrjb0KGf4WjuvHOlm7MQUXmm5TH++HpY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/0717d4b8b05f4122a2163034c59e5f22#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T23:41:59.5250922+00:00"}, "1vUXHSxtQFvvqaL9PVXhPgpd1GZsOczeo6fR+isr8VE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/0dfd9a3304db455a859caab855e678f4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q5n9oh9rw1", "Integrity": "gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "FileLength": 1091955, "LastWriteTime": "2025-07-12T01:56:12.9789889+00:00"}, "sdFF0u75fJYUQO7mXvTnmgIAuCDi7T4JUxaMMbY0dhM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/18e70463ba004856925a8d0e72b2b242#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-17T02:16:16.7082802+00:00"}, "DGYMZC1R13mCyae5KF4LKMHD2ppmyUnXgESa78Z2oIo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/1d66653fc02145d3a7be0572160c3eb0#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kpn9enu225", "Integrity": "SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "FileLength": 1303771, "LastWriteTime": "2025-07-11T21:41:19.6791932+00:00"}, "M1QZj0xgV+NRgFmvxxJ4rLCZeyV3oG7Wnb0sF4Hbjj0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "FileLength": 1607, "LastWriteTime": "2025-07-03T01:41:42.4207189+00:00"}, "7d3hJHDHm5FsOJSFI12ukVvqOoXCEw01mgDFbk092j8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 7630, "LastWriteTime": "2025-06-24T14:41:24.2656661+00:00"}, "1iAipClIP/28z2b4LmT7fZq3IvWHoUjbabl5UmOjaEM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/3765e865484c4ea39fc07eb5a9182354#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:24:28.077873+00:00"}, "iO690+TBbeCEcsgGRMti30rZUuQrApLbDPrYXOFXBFI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/37b012f3d1584ff38e3dd454fe705980#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-08T15:05:30.172796+00:00"}, "svNer+x1BBWuxorInGjNKIoGxwrmttAyL9jC3FElFgw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/3982df413cf14caf9da852e86398c3f2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T17:35:51.1150378+00:00"}, "Nw545vydZ0NpWFzool/ptfMXvJMWexQPtkKDuomHjbE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/429aa494b4f6454aa6a35873f7c8401f#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mqlqasmzgb", "Integrity": "crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "FileLength": 441546, "LastWriteTime": "2025-07-23T16:01:30.4726244+00:00"}, "fqUVTZvtJ7ZMHZl+gfXGOfX3cVCuF/hSocC4JY0Ksx0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/458bc2d8a56c42e4aca145ca1cdbf135#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-13T03:26:20.321203+00:00"}, "+ifrVhPVyeYnbiXasePNbh2VGPmv/Nat2qQD55xTsN4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/48082c73ee21481e8b2b10f9300d66bc#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3cceavjbna", "Integrity": "80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "FileLength": 1226126, "LastWriteTime": "2025-07-17T01:07:50.0012845+00:00"}, "pvNzvuRCg5iueyZl7eT61GycZuGiZk2yWlXsIyx/qEM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/57cc983c754e400fa8630e31eae1bc49#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T01:09:13.1326598+00:00"}, "tdmd1AzMR+MZu23+awGL2HYcduMvTfS2CsdDwVAz/mU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/597d0e98415a4c19b8d7267d6e4c096a#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:27:45.2139157+00:00"}, "RMlEKN4Abqubsag3hywTE7X7DY1qUbaQykVKIHaYdLI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/5b9afba8483c42c688d10268e402bf63#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T21:05:07.0359648+00:00"}, "0VZIjBMkI3NlpZ9ouS0BmtKqEjHDM8w1gT+GUl+HshY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/6d49532932024faf81b6cc20c91ba142#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:24:59.5080405+00:00"}, "5yyGiDESKl7yeKkm12Rq9oQr1IN6f69x4INbs/B6uHI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/7091b5e0eb5f41929191868a1ccdb942#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-11T23:45:56.3817563+00:00"}, "5kPPyKxXh9LMCV0m72x9a04XJXJnOsUwGv4IGscl2IA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T16:05:03.0221313+00:00"}, "GsUF/Bk1Q5dBMBXfXFQQc9xM1NKhOzHmNyGr1pp2lQA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8375e14b17a341efb982c00a4344db2c#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-03T01:46:36.0382212+00:00"}, "XDlxoN4+PDdfhvzRc+swxI//xTnw/MdeH4kHYHo3Vjc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/838f2c3b1144437183c209880c5ddf08#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lnw1de61i1", "Integrity": "kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "FileLength": 7245, "LastWriteTime": "2025-07-17T01:41:42.9127455+00:00"}, "pDbUAGMCV3tyF8bY6YbrbDMC/YZGH6yk3WeDmXV1vYk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/84b9165f75994121b64d6ab3a05d38fd#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-17T03:14:57.7093231+00:00"}, "1X1NFxZi5t7ZPMUpsPIrJn33h4mrO2hhfcgpSNcQpwo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 7630, "LastWriteTime": "2025-06-23T16:18:46.2687981+00:00"}, "FBrkA5sfHgqONe9JIBZ6nfOZX+qf1L6E5Uxpt7FdcXM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/a24bf33b26f34799b3d0330291adf0fc#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-17T02:38:09.273107+00:00"}, "UAO84WjNXPNrtessSME8hh7/b0oRI1g9jC7xF6HvyKY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/a91fd552aae140a186b9f82e91f6d071#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:22:42.3130085+00:00"}, "Iq8OCo1q4ah3IZwViwNuO6MTiI0lMNdY54pCtRO7358=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-23T15:48:23.7977913+00:00"}, "rfEs9jcBXXQmciRsA3V59y0YOGzQ6GdNYOtmLnT2cfU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/bff0abcb17524585a06eee3255b1f49a#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "FileLength": 2149873, "LastWriteTime": "2025-06-29T00:48:53.156964+00:00"}, "ncbu82uc8MJ/PzD8QOQmFqr3/4HnAp0kCcjrAR1zyfk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/c3d68bb6815944c38541f95cdc697682#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nmzbkjlql8", "Integrity": "4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "FileLength": 23575, "LastWriteTime": "2025-07-17T03:34:44.4541722+00:00"}, "7vpP8tKIsabGKIYd5NeqNACv5vUrai5IiQ4b5Eg3O7A=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/cb01a1c7ff704836b8a4575cebba0f7d#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nmzbkjlql8", "Integrity": "4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "FileLength": 23575, "LastWriteTime": "2025-07-17T17:26:45.2718914+00:00"}, "p4iPilNza/T/FYIBQkifDQPGG8ERdm6MYoAuAxGW+YY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o5mrvqq7ms", "Integrity": "7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "FileLength": 147799, "LastWriteTime": "2025-06-29T00:59:59.4341293+00:00"}, "GRyKd2qxDLLEWKW9CGuCWCBcjYGgUXPDuTEvTjrM4Y4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/db26e8581e504a6fbe89c9f2c25ea049#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-13T02:30:08.8642844+00:00"}, "iQAhAIzxvAqngWtUOv8u6XW9RmWm6phHiIie04LrPLM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T18:32:56.9292455+00:00"}, "Zna7SwJDBUHWuv3PzxLWIfOkaQyOQ9IVvXfM0Vm028w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/ea02708c3aca4feca7c6fddf23666bfe#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-17T01:07:53.8440146+00:00"}, "wMQEqglctd2NqfBhltuUiIIym/MWPKI/FWuHb36XKBM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/ea1632403571458e8fd9910c56d0f0f5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kob432tvll", "Integrity": "JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "FileLength": 301140, "LastWriteTime": "2025-07-08T03:27:31.2464409+00:00"}, "1obPk6u8AhaPYoU1qQIKtuPlWtXd196oZT3mARJPOVA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/eb65cd3797854633828ab9b66feaa963#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T22:34:48.584025+00:00"}, "a2p6Na5B7IKLife1PGBoh+SpaBLaW/iUI66bPNBDAsc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/f3aa1eeb1a4a43f39ee2622a9c538fff#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-13T02:49:03.917942+00:00"}, "dO+67+xCd1I8LnXU0l6L7kJkLEdUmwD/YegGMrY8OZI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/f5a02559328b46e78d0360bce78e29a6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-12T22:22:24.4791194+00:00"}, "vS3/q/t36ExG22y4DTamXh51FkWZrIfnifxr9Us8pJo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jilall62b3", "Integrity": "axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "FileLength": 63019, "LastWriteTime": "2025-06-24T14:04:39.4164267+00:00"}, "9mCpvJluBojvSCFt4ks2EartcsINoVd9faaHEoR8POY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/012f27a40a4b47e6ae5db437acc75f99#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "FileLength": 51973, "LastWriteTime": "2025-07-28T19:06:45.0690293+00:00"}, "kOOFfpUdKR36Yq5VtNQ4lfq3dSV7tVipvbyFrkGHPW8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/058264df89934078a8fc82a563396495#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "FileLength": 196383, "LastWriteTime": "2025-07-11T23:54:44.9383661+00:00"}, "CO7x5DSbsIuowarMLwIKA1uILeQZ7i79jC1Gz3f2Y8o=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/0927b9a8ae0b4ab991bd710ade619ee2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "FileLength": 195627, "LastWriteTime": "2025-07-23T22:16:17.90328+00:00"}, "puHsqH7urbW3xZUAbBO6zqgfuxdOa8bbs6boMzRxmxk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/09adcd0d2d104d32ac9533b83026a8d1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "FileLength": 196383, "LastWriteTime": "2025-07-09T21:10:49.7405907+00:00"}, "+FkH8Duj3p8lL9bLZ9NDYQDQzJhR+2v8XxHm3qqubFU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/10052d090a244fe28441690bd549cf4f#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T21:49:32.5495896+00:00"}, "hb1p5R2rPvcoh8DFJHfHFVGQE0HlpAMrJ5UGmbopGQk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/158c4d49e0444e039ee77f372cb74346#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "FileLength": 726022, "LastWriteTime": "2025-07-11T21:42:20.8530144+00:00"}, "zHL9cihnagFLK3h99yjtb4I2wKBuZsj2obTWtC9qV0o=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/16cbcd9c40c848068b1303ef10915a19#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npx0pid61y", "Integrity": "qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "FileLength": 890, "LastWriteTime": "2025-07-13T02:43:16.3621425+00:00"}, "h81dwrVRNaKjkXzSy5lsI+ur7/Q/Y2wHM+mfx53tWAA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/1870288851874090b203bf27c9321fed#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T13:55:30.840068+00:00"}, "x3q8WKiRJ/nEvo5ekSarmerIHYg5GohsZ8L+aikIb9c=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/27cdd4e56358437d960e875392a5c2b1#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "FileLength": 97050, "LastWriteTime": "2025-07-08T15:19:37.2509081+00:00"}, "hkXlxku51MH5sjL1y+8xOT5IUABVOX09xnCOQjiyZAI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\294d4ba8c7134ac2a9e6edf81fd61fd7.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/294d4ba8c7134ac2a9e6edf81fd61fd7#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\294d4ba8c7134ac2a9e6edf81fd61fd7.PNG", "FileLength": 1011297, "LastWriteTime": "2025-08-01T00:35:19.3100212+00:00"}, "6jCipdlU3ZTIiwUSMbo+SDc+IkpFH41EZGecCKKaD/k=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/312205ccd18f4650bef24653fbf9f64a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:45:00.613941+00:00"}, "PlO2aWxNFcYRCwV2hBr4lfTPj+U6saYfV/91bRqgJa8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/314d69b11b25437fa3e677cb0bc0df70#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "FileLength": 180218, "LastWriteTime": "2025-07-13T02:42:50.4992439+00:00"}, "otfX7cbfzrCjHrHBYEn2aOhmtKBOKa6Z3pqmQ7ysOdE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/314ee81838e24ff69f9376e5ad555018#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "FileLength": 195627, "LastWriteTime": "2025-07-17T16:03:31.0481992+00:00"}, "kH4mMZL2anSpCg3HI++u+gWcO3bB0kPxAf0hNHzOYVA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/338974a63b324a6f8e140174a2be9358#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T15:32:06.0778159+00:00"}, "ih+IqZEshrO6IoU5NLuu6BPwd+r0ZAGjhsUf61h7054=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/34222e73c2af41da84d4996eb1794010#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "FileLength": 195627, "LastWriteTime": "2025-07-11T23:47:46.5583498+00:00"}, "lMI3ZdMzHr29GHk5Pj07aGNtVg8AiPOBnbB8IaKey6E=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/34a889f3e0ce40e085d25f74812e870b#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "FileLength": 726022, "LastWriteTime": "2025-07-08T03:37:20.1103776+00:00"}, "u2E6twIKUPxyUC5u3A97tGpI428fauFVZ1jqOQDykRI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/383b2c9c091843a698a7f649130db384#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c40i8h3quo", "Integrity": "HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "FileLength": 70779, "LastWriteTime": "2025-07-27T02:42:42.0982307+00:00"}, "UGBkA4yg2tNm9HBSGpOOS2zUmp+X6r5evvjUC6YU2Es=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "FileLength": 1280957, "LastWriteTime": "2025-06-23T17:18:40.7316671+00:00"}, "X7eRFM8JMXMepr7c3vsNDyNFRc4VUsZl9CHGtXamtsk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/43ff42ee0f494b1fac40397ff6cb35e4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "FileLength": 195627, "LastWriteTime": "2025-07-13T03:32:42.3031732+00:00"}, "99uB0UXlLbf3KX3jecS3NvXWWGysZKAQZYzy3rgBlxU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qt5iiuu92c", "Integrity": "/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "FileLength": 14596, "LastWriteTime": "2025-06-23T15:16:46.5076693+00:00"}, "EI7VMakM5LQeUx8uNkrgMrsr/DQiIT35BWRnEjhdCXU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/48d3116a39f34661b105e97351e9dd65#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "FileLength": 86139, "LastWriteTime": "2025-07-17T01:23:53.5401569+00:00"}, "AvF6iYvDaSAEGH1VM4Cteb+8RBJPKNTjmH+RJS6xspE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "FileLength": 853270, "LastWriteTime": "2025-06-23T17:13:46.6310546+00:00"}, "vmB6DCPoPXgcQ7HjD3+G+zdf0bYUUp93Bk+QLHaLpPs=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4ca45322624947dfa4c15343a522edaa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:05:46.8044243+00:00"}, "Kk6xLmSbYOvZrzrY3W9nKxm8nwW6LhJ+uM11QpwwoYc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4fa649b91a96437e8b2da7b488ff9c72#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "FileLength": 196383, "LastWriteTime": "2025-07-20T15:37:10.8536851+00:00"}, "Lb+6fEOlPebg2yOwPld1J0RoC/23s8wVTHRtGkAyvCY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5094f812e6634ab4acc07ed00767abd5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "FileLength": 32278, "LastWriteTime": "2025-07-16T21:21:58.8318283+00:00"}, "zoFHB1rpsjcX7obPGe169yn1DMpIdyqL9cqTe8JKh2o=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/53e82cd4346e4263bc415d38312d7b49#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "csvf1bo9y0", "Integrity": "VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "FileLength": 158471, "LastWriteTime": "2025-06-24T14:43:26.831517+00:00"}, "Y/6XpapjKKQfnG1LsXrHYrR9zPGz+I9kc+GYlXWJTyY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/59d9b375bfc749ee94270d0724e0d922#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T17:14:59.7575806+00:00"}, "Rv3nc5WR8gQhsPpo+r17l81kQa8qNnhVkvE35k80et0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/59e3e01493f748bfb81c21759fbe6502#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mi7mdsl38n", "Integrity": "cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "FileLength": 542436, "LastWriteTime": "2025-07-28T18:27:20.9593192+00:00"}, "fvctIhDgvr8ypf2Mj1VYGBqT1YjLU5LXUAsSfDhUn8g=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f111548ca9e4c5cb05845290f0b0eb5#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T23:51:50.6875979+00:00"}, "d7JwoWNV5PIq3QtEDoqWGz1ccSzxyC70hn/XknsX4pA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f82d034f5fc408991373eec12a04772#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-13T04:23:42.4282445+00:00"}, "Z+yQYGf3wt2X9yIh3dvfOvFZdYE9SAJITqN+5ry+ZS8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f8765d1b6124a7c95a2923a03bbe7ab#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:43:40.368196+00:00"}, "AUzcoW38qLtvouVuHTuXGRccYqR8ExOL/sqH+Ari9CY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f98bb1a22fc476f974000194d3ee208#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T23:01:48.5934179+00:00"}, "XWlEfuoKJl/E3LYQkVS6TtnECofnv5yig9evLoY7L9c=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "FileLength": 97050, "LastWriteTime": "2025-06-29T01:16:18.3219509+00:00"}, "L10HvpWA13+JD3NP9fyHGEQ0nq+hFUUaLukC50T0hyY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/621c79053943489d9838496dccd18b32#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "FileLength": 196383, "LastWriteTime": "2025-07-23T22:11:33.9140228+00:00"}, "XlDwoX53eK3FJxk6A+sSV6c5pDSa7jwvIvoJP58Kuac=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T15:23:15.091352+00:00"}, "juvXZX+UmY0KLvdDX3TTMKPSiZmFqx2Pvp0biwENCj8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/66ca76d3dec94b7fa33213be976243b2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "FileLength": 86139, "LastWriteTime": "2025-07-11T23:54:35.1834786+00:00"}, "GABg1ejfzTU/1yMFjd0PdiOi0pB1l+k6v8DFODhTITI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/69b88efe4c09475aba9ea7025d4e346f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "FileLength": 726022, "LastWriteTime": "2025-07-11T23:48:14.9945875+00:00"}, "5IfGJz8CtH7yumXTFISPfvjmE66xw2OPXqhoepQ1SBg=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6a008f012f2b46a9a77ddbed720921cf#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "FileLength": 195627, "LastWriteTime": "2025-07-23T21:57:08.44001+00:00"}, "F/RR0qGobGLAywrn+qtBawbsl4hqYWNW8zHIB58IGbk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "FileLength": 1011297, "LastWriteTime": "2025-06-23T14:50:15.2248065+00:00"}, "Yr4i0RiT4pe3jrG6x0mxOHrzCOxO/udRab7gHPKQ3N4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6d8d0634bb084b3380094f266ed72df7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "FileLength": 86139, "LastWriteTime": "2025-07-11T22:30:11.8179613+00:00"}, "EDIbMb+QFamsOss6QvWtPcVIWWOa3xwMEWPGjGgXVNw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1nfn82ehfv", "Integrity": "E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "FileLength": 1345158, "LastWriteTime": "2025-06-24T14:30:26.795147+00:00"}, "Y4ZJ0nolwkiVL8jnUmI2hKZ89B6Ix1YLQMwb8r/wOpY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/713c55958e434619b8dbd17a5ef62b7e#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:21:07.4194605+00:00"}, "yJN0fbGpny7pmUP+Ivu7fccYBjmGidrvoMJObpqv9fo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/73ef279aa84b42e1a830b0c1747a9baa#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T03:11:00.9850676+00:00"}, "bRhfMefJB2wClV4abP+VXtYNn5D7o0wTlhCl3GGWMVw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/757730977ffe43dbbdef2c17e557a5cb#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gs16logzrz", "Integrity": "kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "FileLength": 65595, "LastWriteTime": "2025-07-26T19:17:41.4103545+00:00"}, "ecDhX6KBdU/4qNBdeuOpFledsh7LO9tc1wiEllAIsu8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/771992cb22384d97bd1f5d0718a5bac6#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T19:03:03.7502302+00:00"}, "vVV1s4pLY8uLaIc0Ka25cqMtW6wZCYMHAUKm1zj71SY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/783de52104b84aac9fff70eb4857a083#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "FileLength": 97050, "LastWriteTime": "2025-07-13T03:32:07.0991612+00:00"}, "T5UjcNb3dTVVQgYgQxG92SvxORkLh5g1JZoUXU5YdR4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8422115944564f0d9b5336c152d37758#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:04:29.1945952+00:00"}, "j37Xdl7ErCIy7pYsg7GouZXZlwfagKMreWdQlZixGEE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/866b5614a13843f5a8c082332a886e8d#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T19:04:27.9949923+00:00"}, "tvEsZBs/+VmnZ+XVGSgxxaBbwE09qigMff3rkNcNtcU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/86f9084a89de49dcb333d63c0bf993b9#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3oz33zfofh", "Integrity": "UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "FileLength": 87614, "LastWriteTime": "2025-06-23T16:15:05.1761193+00:00"}, "zVH9cPLC3XKvkYsdKFUYf/nd9ULh2JxFUBTPspe6p9c=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/87959591ff1d4bb9b6d3cf9617456555#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T14:16:58.1174787+00:00"}, "JCmTzhRRb+TZ4uiNnIcR3Rliwubcy8wbcitxNv4F4mk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8891458a31864b6a92594c256956cc0f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T23:01:25.741061+00:00"}, "J0fRpSANXZkwdXtKVkXamW9DF2BYhVGiBri9KpfIuGQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:36:28.1772026+00:00"}, "2OWggBV6Mygwf56er0WDupa9Fwo4GxRWHtwJlB64uvg=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8e75782283de4a1ab7da0396404ac48c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "FileLength": 196383, "LastWriteTime": "2025-07-11T21:58:59.6922065+00:00"}, "CHK6tvJVUCrFoNWd7UfOFpQEnbgiMr3V0aD0oW39LOQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8f38d922b2a6453e8d9df2beb9012c53#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T23:01:12.531021+00:00"}, "a3AafGcgN3bWTTV2xwXgHAF5ud6gkPiFwzX7Rr9cDkA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/90fda8458bb9455ea6069717e3093d03#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T23:42:47.0154538+00:00"}, "Oof8jYzNsBP/6IRadLyOlhnszUrQxj/s0zdbtnLrqZY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9130a0bd7c4b4a3599d408a4fe24745a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T21:11:58.7925204+00:00"}, "gMr3EvWO+hEgwLgOYXJQjVoLNRCyB1iPpiC9ZO2Bjcw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/97b49fcfbb844432a4b311cb199e86f9#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "FileLength": 86139, "LastWriteTime": "2025-07-03T01:54:38.7796004+00:00"}, "VT0g1dGviXEEvX/+70CQM3TKQiWJQ/W9SqVA+jrlCb4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9854cf13659b4bb6a2141d0824af4666#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "FileLength": 86139, "LastWriteTime": "2025-06-23T15:07:12.4967137+00:00"}, "bL4VMjLuTtVRegYxotlNQo6SWNPwhOzkZxe4udpLz/I=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/98a77b5c42654bf6b3f376aa6860eed5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:47:50.6697673+00:00"}, "E9v9vaTbWKw28k93W6V/ElVxtIOWsb8CaBlJS+f3764=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9a342b7e0e404095a917be1fc40364ae#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:50:41.8848937+00:00"}, "jYXOk6JJW3c1XdyeFatgKNsrEbD2BS6qWYiT9dkkVlo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9a7cecf2a2af4dc88583fc5a439133cf#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "FileLength": 97050, "LastWriteTime": "2025-07-09T22:28:29.4046496+00:00"}, "/NviJnZhIWy91npIwMRK0hV2nUG2dhQfNaT5jjRS33s=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9ac777271fb344708f6133a9f76a4bf5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T19:05:12.2340174+00:00"}, "Qwhb9tTgefx9QisYkGbwbT1g4Ur6bCSeh4PurzekzO0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9c71ea3b963b479e9df40428b62504be#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T18:33:05.3750627+00:00"}, "b/2RHfz/l0qPm9dy0k6h6pvz/6Q13IHSK0DSPldaL7w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r7rmd0638a", "Integrity": "uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "FileLength": 155911, "LastWriteTime": "2025-06-23T16:15:51.1717568+00:00"}, "JJtHTNWlYiIdeh+M/VnMIr+E8nZMEkXQFvPdIzI9ji0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/a64a54a1298e4b8c81bf46d705996980#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "FileLength": 853270, "LastWriteTime": "2025-06-24T13:31:15.0724542+00:00"}, "a1NuCd0fz74cGl/oyR59DHuUZ4+vff/V9r78bTRnl94=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa469b655153465e9cd1265d5a36a8dd#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:41:59.6848191+00:00"}, "kJQVtesQAb/dJ/Z/ex5k9pHyBFWF4SCD8RuEyTZe9G4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa812b7960134dd380c2c5fcc1286289#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T18:31:04.3491046+00:00"}, "EqFU2HdeilVIcSNpMzvvpTrnD+U6/5M+ONn//p2LyrU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ae1bc4df202d4ee7ad0662c86c040945#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "FileLength": 32278, "LastWriteTime": "2025-07-13T02:48:17.1588688+00:00"}, "Idt+PjHtkt9181zCl+O4T6862sTePRk0NdfGUXPyal4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aee04329d556487dbb70eccf248638db#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "FileLength": 196383, "LastWriteTime": "2025-07-03T01:48:56.1935401+00:00"}, "yoWmh6/mm6W52D1I977S8t9+cETkMeqP2AY5Jks2h2I=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b3ac325587274e74a2f539ae88f27bf0#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "FileLength": 97050, "LastWriteTime": "2025-07-23T22:08:28.0399766+00:00"}, "cm5uZ6LFmzJjDrTXQZEGNh29JDlmNvILl4tVHubOhk4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b3c8c8f821c4448f958edb509d8ca866#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T19:07:26.1494472+00:00"}, "VoJ8wI8MbKS6UOwJLVoc13zbeHR6vNxhVe2xfv3FZyE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b54042592d954bf29ca490c559a2c12b#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "FileLength": 726022, "LastWriteTime": "2025-07-17T14:48:20.3363536+00:00"}, "GrcgsfyxFbChYD3iQZWINLc3jl6tQ7BSUjoQzY9nleM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/bd28a12d15e24fb48511eb051459a6bc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:07:29.4671985+00:00"}, "d+9Ft/q/YOYO95IGKopPVOV534qQ8BbA6lf3dxHAsgw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c0a67637111e45419a60d3d548235d0c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c0a67637111e45419a60d3d548235d0c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "27400mnfne", "Integrity": "Pl3JMG8LTSMMPSwWEJH3I6BdonB+D0hoDJn0svePR+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c0a67637111e45419a60d3d548235d0c.png", "FileLength": 315053, "LastWriteTime": "2025-07-31T23:01:21.6775511+00:00"}, "tCj5tUJF6sqatVrCjIMOE3i/pWs2JTPgb8K/8T3/lO0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lb3ihrmur6", "Integrity": "r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "FileLength": 62981, "LastWriteTime": "2025-06-23T15:16:53.6500292+00:00"}, "367ZOCts2LdMVV7T5dRnu1gItmtInxebqPPOxjzv9Kc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c2e7dc6ac9f743a4b5443b55463b3766#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sejuu0i0l0", "Integrity": "PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "FileLength": 66359, "LastWriteTime": "2025-07-28T19:02:36.2233606+00:00"}, "uMZzCLl5PNBI3IFol7XRckr3tgT+zf3Hfyfe7sKmCvw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ljkl4o8aee", "Integrity": "v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "FileLength": 3582183, "LastWriteTime": "2025-06-23T18:33:51.7083814+00:00"}, "67y2oSjwGZGO/iBVPTbaWpqRFrN7OgnkepJBXy4YzvU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:51.8740204+00:00"}, "i2uPQHlwXbfCEeTF0iCAfDud0dGPFgm/xRbYPIrti9s=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c835a2f77dc6491195963a76f091b3d8#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "FileLength": 97050, "LastWriteTime": "2025-07-18T22:21:49.0683534+00:00"}, "gI2IN9t0gV6LE80Fgy+E3mPj/gj88MSaBfyd9Ptj9S4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ca0161389fa346e6be985efb101cf3f7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T15:28:24.7705336+00:00"}, "BK0EirozkIIh8MZb/VNwC3sF+KiQSGCR3BA7NMY9bGs=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\cec71fa098124067a20cf32600f1008c.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/cec71fa098124067a20cf32600f1008c#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\cec71fa098124067a20cf32600f1008c.PNG", "FileLength": 1011297, "LastWriteTime": "2025-08-01T00:24:58.505205+00:00"}, "YxdwYXKeXtx0SGTfBsuQm2CqGoxYR4DmMj71x5WUEYE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d0f43a0c17dd4ae78ab837ce42692072#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T21:12:11.0084541+00:00"}, "JPVFUTJR1b8R52//81vSobTzaOWPoWPxt1v7CUY3PY8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d277b7275c644e738414f5c2bc40bc99#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "FileLength": 195627, "LastWriteTime": "2025-07-08T03:34:52.8371931+00:00"}, "62eNWqZw0OrdoyXGhXNQEXXnzFJ5G34B40ABhBefk/A=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d36bc059c16b44bcbc0a35050a0a0b5e#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vcjj9guxre", "Integrity": "v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "FileLength": 46057, "LastWriteTime": "2025-07-16T21:21:32.7519644+00:00"}, "znPKdJzyAyfiLuyKCzs45c2lrbhTqdb7/UYjSkY1NpU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d449dbec51c3439ba07b61795bee9e16.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d449dbec51c3439ba07b61795bee9e16#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d449dbec51c3439ba07b61795bee9e16.png", "FileLength": 235486, "LastWriteTime": "2025-07-29T00:16:21.0366636+00:00"}, "b/vbwz5HEsGKHTj7h2EUKtm267GXqH38qLQEjLL+aEQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d5ef1ef2d081452180eec81c36b2b8e9#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T19:03:55.2635937+00:00"}, "IHFi2VxxRGBkatfE7LLKSU7iasrckHcDIRmvmqm8uD4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d5effe60e3e84110858bb6b1629056c4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "FileLength": 180218, "LastWriteTime": "2025-07-11T23:57:37.2157052+00:00"}, "LdGf57kDvRWu+h/jUD6EXdaEsMDUw/sQyILfJnpc2fU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d9e709f5e5be4ee4afcff29995499f15#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "FileLength": 180218, "LastWriteTime": "2025-07-13T02:41:42.7465056+00:00"}, "PMYezIFaDPAhIbqOjZeljV/ilqoJW8+jJmQ2BjRpwnM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/db5e4060478d44bdbd0eda3592b741c4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "FileLength": 195627, "LastWriteTime": "2025-06-29T01:16:11.6030496+00:00"}, "JyEBTtQOULVIdMkOGBgS9X2+TUvlYpCJNgrHIdUmuxk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/dd0c100c1a6e43efbee4ea0708b5eefa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "FileLength": 86139, "LastWriteTime": "2025-07-27T02:42:45.7108249+00:00"}, "sJ+eEaC94oNrX8vBg9TqeNPAkli/FGUcqTh5CDDKmSU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/de20a39284b94cc0b97bb816eb1ac085#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "FileLength": 195627, "LastWriteTime": "2025-07-17T02:34:23.5270024+00:00"}, "u/lXJosLJbgN2KDwihvy21YjKX2GLVF+ty7N4WsQ9F0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/deef804fae0c4fa08e7b1b901925419a#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "FileLength": 97050, "LastWriteTime": "2025-07-18T23:45:24.7875306+00:00"}, "jUSpJAw5KgISTBeHyKHG74n22pvqA0R4QSQPtYUPWzM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e7ddc387739a443ab47f3c0d18e7cab2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T18:36:40.3986047+00:00"}, "GKMLw9ZvouAssKt4Y4hk3Jn5pMkJOqHCt5P23rZ+YPU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e93214cb0d0c4b0383f3980f2c66f6b6.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e93214cb0d0c4b0383f3980f2c66f6b6#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e93214cb0d0c4b0383f3980f2c66f6b6.png", "FileLength": 51973, "LastWriteTime": "2025-07-29T00:16:37.4785195+00:00"}, "/aCw6FvtqTZFrbKyAAln9nyILgLoPhpwKtOfJ0nXJhc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e97afe0fea7d46fa90ae97941b717777#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T14:51:09.8230719+00:00"}, "T7fftO/8ZjwwI7JT4s1Z9iMEq4NQ0gW69W65HuX0bSA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "FileLength": 51973, "LastWriteTime": "2025-06-23T15:25:34.8705091+00:00"}, "OowhQNgvfN9sQw0dJrTAs7a7PA0Hh4g9ApPUNYQXQF4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "FileLength": 1280957, "LastWriteTime": "2025-06-24T14:09:22.5652244+00:00"}, "nDCpVwkq3dqdtKKE9SQ0oJYgxGAiiRcDAqKzSVWm/Pw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f5fa12ce51fd47b39a7341711e685eeb#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:46:34.4451688+00:00"}, "/t8NOfsk9V77CKXJ/iBNFRp+QprUTYtLIKRMMuIout4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f706c5792702492a90e00628f2eb9ba6#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:42.4916683+00:00"}, "kKe1Le6DVLmUxlZewmABFqdxl4LveWFoyzKNV9qHXDo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f8a5a58c11b945118610694f03b0007e#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "FileLength": 195627, "LastWriteTime": "2025-07-18T23:44:54.2234929+00:00"}, "ZZEOjH/4wdcbf5rpTZ1XDC3m/2t521DAQx4vUbiVwME=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f93ad881856442cd850032c437ad256a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "FileLength": 196383, "LastWriteTime": "2025-07-13T03:31:16.0982187+00:00"}, "YNfnAaNKV7BXERvWioMZ6rKN7eRiVnZleR3Zozw6cKU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f97829c0fe164622a2e1eae38e40117a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "FileLength": 216207, "LastWriteTime": "2025-06-23T15:48:04.7058017+00:00"}, "yK9AEsf3zKS3jNygseVBh7zq8SyXk+ZDGc43zin14CA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f9cfae0b5e74436c8a7ae3b09af4e0da.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f9cfae0b5e74436c8a7ae3b09af4e0da#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f9cfae0b5e74436c8a7ae3b09af4e0da.webp", "FileLength": 97050, "LastWriteTime": "2025-07-31T23:16:52.3966925+00:00"}, "kZnfe2f32eKJAzNWFPOpAwZ6T+Hx5mxZov451Kfo9tQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fb22d922074c4743951c4e34232de868#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:58:32.0738982+00:00"}, "AwIRwycqEYKfzwC1EPZ67iYGrgJuvCU94v4qIPgcpZk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:50:37.2982217+00:00"}, "eVd5Olhicxk7UvQ0zgE/I5PyGApyhLX6sXtqaiQaYQE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/feda33a57a2243d2af4b1db8688f2f88#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T15:32:15.9848054+00:00"}, "81qaPfAJ2rfkytC1JUH7IcCRUZTSb5RIyfDPj3gmw1Q=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ff4a6481da70456989622e13b2250a71#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-13T03:26:48.7392849+00:00"}, "CPbGMnS9GcZQ9YrwDiQ+yr+shEY2P9oLOfx96X5940w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/11073ad0-55de-42bb-bc0e-e0a215069961_10#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "FileLength": 32278, "LastWriteTime": "2025-07-14T19:49:19.1638785+00:00"}, "eSWvwjGILUMVLeQrl8wA+IxNIUD65WTKsn2pHnOZlSI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/4b03c2c9-c7c4-482c-91d9-964d60405f38_123#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vcjj9guxre", "Integrity": "v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "FileLength": 46057, "LastWriteTime": "2025-07-14T19:43:57.0564612+00:00"}, "Xl7trBvM06iX/0MblmaVedfoppgOVcifNbSyYucD5ek=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "FileLength": 195627, "LastWriteTime": "2025-07-14T19:58:39.6039832+00:00"}, "pFGCkA0uD4N59pZHnPMM7Xi/5Mx9miUISt+Yv6zIvAc=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T20:24:05.9609087+00:00"}, "XQbQWwDHA4oygDU7UbiDhv5H16YXqXk8mScs606R5bY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/6da59366-0bed-4102-9711-e5f17991bfb2_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T19:51:33.7638406+00:00"}, "A91MheFX895dWCb6A45EJFqqF9Rz8Gw0OOauSxXqmow=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/b8061978-90a1-4e3d-b474-328770509051_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T20:09:27.9462273+00:00"}, "x9Q2P+DAETPjJqk4N9ivLi3DsDfqccaKjgFKbYr/eaw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "FileLength": 97050, "LastWriteTime": "2025-07-14T20:27:49.3380591+00:00"}, "27X7krisLuwkH/NdZIxPtCgWrKqjnVnamffZGIZaXd0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/16_20250716_025728#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1756gt4fxa", "Integrity": "M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "FileLength": 6260, "LastWriteTime": "2025-07-16T02:57:28.7613584+00:00"}, "jF+eQrQnwg+X+Kg0yWZ74DfpUuvK3hIvLp2P54queTQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/17_20250716_020611#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "FileLength": 235486, "LastWriteTime": "2025-07-16T02:06:11.5923898+00:00"}, "U3QicamCG19Ow/9TzCW043RC6JW5FM7Ts35jmNwF5m8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/18_20250716_025530#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9zr8ou7lzf", "Integrity": "pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "FileLength": 11549, "LastWriteTime": "2025-07-16T02:55:30.5271881+00:00"}, "IDH7QaQ6VaxvXaY4fZUOiIXXQBnSPpbSb42MMo/54eA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/19_20250716_025433#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zhkndqnwdu", "Integrity": "efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "FileLength": 11348, "LastWriteTime": "2025-07-16T02:54:33.278385+00:00"}, "vIKQpIPeO3uUp+Sy0ri7LSzzEuK8uFtdl2rGZkFR0Ss=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/20_20250716_011214#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zl0dp0irm3", "Integrity": "qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "FileLength": 30508, "LastWriteTime": "2025-07-16T01:12:14.1191614+00:00"}, "W6AFCz/o9z9mf5YszHpsOAd3RIqcSHIXtTMbeA8Erv8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_232513#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:25:13.1246877+00:00"}, "nI08cSxAs1ywX8gHwfly40SNg1C3hEHRHi8ji+22agE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_232624#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:26:24.2891095+00:00"}, "QToCUuKCUOZXx/usfyTwyiblQfjRXrpbmbo+qEHIzr4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_233121#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3nzxj0mxb8", "Integrity": "r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "FileLength": 20215, "LastWriteTime": "2025-07-15T23:31:21.0105187+00:00"}, "i0wxnXgQXTJFc7olgTdzb8xa3PyENHQ4zqeqa23dUfQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_234020#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9xcst1d5jn", "Integrity": "bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "FileLength": 29764, "LastWriteTime": "2025-07-15T23:40:20.1985649+00:00"}, "4W0Bys+H3E25NZVqLyhwv3UBsUnHt6QiZSQ59DRIQ6w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_234816#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zxkacwu4rx", "Integrity": "h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "FileLength": 9693, "LastWriteTime": "2025-07-15T23:48:16.9563145+00:00"}, "XZMRm/mYa+DsC5TKa3lu1koc33m518s8FuyBeEtb8U8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_235443#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:54:43.2312954+00:00"}, "+pC+7f1nyDzDKnCtdvXKj8TfZU4vTnjH+Hj9MWGIHwQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250716_011418#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ioah74vzm7", "Integrity": "UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "FileLength": 54472, "LastWriteTime": "2025-07-16T01:14:18.8359357+00:00"}, "G+bOWz5GvCh0TB+gggW6ytCYJSpkZVxPiS67SjLEB+k=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250723_161244#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4cnl9qiz9v", "Integrity": "ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "FileLength": 20451, "LastWriteTime": "2025-07-23T16:12:44.2699801+00:00"}, "SnnfihuzjHB0ILXPWs5uPryAWNsLm/ssbQuxmazBitA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/22_20250716_021402#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "682niy8knh", "Integrity": "+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "FileLength": 9849, "LastWriteTime": "2025-07-16T02:14:02.8686609+00:00"}, "X/K8mf399zmBF5d0yXvcGzpcrBEtB9inSrx3z7TWX9M=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/23_20250716_021342#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vv893ww0lz", "Integrity": "O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "FileLength": 11910, "LastWriteTime": "2025-07-16T02:13:42.7896414+00:00"}, "OiCl6m56f9leifefFgjyJ1RcL8bL6iM4b1hxJg5nJhg=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/24_20250716_011509#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8zdxq49pmb", "Integrity": "/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "FileLength": 13303, "LastWriteTime": "2025-07-16T01:15:09.5270347+00:00"}, "lSf/PdL/BsbyAzctI9Ex1MWWVFctrelE/d+aah8Fp/s=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/24_20250716_021311#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l3bd4ywp3d", "Integrity": "cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "FileLength": 18938, "LastWriteTime": "2025-07-16T02:13:11.2486237+00:00"}, "/MqwXMnbgz4Cvd26VeC2m5klWbeyhpJ1cUSJ1lGL9UM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/29_20250716_011138#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zxkacwu4rx", "Integrity": "h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "FileLength": 9693, "LastWriteTime": "2025-07-16T01:11:38.6675379+00:00"}, "l1wohJhVbtRudLATTCRBRIhtTOtNl9et/Z1P2BlpXn4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250801_164559_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250801_164559_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r1feqlb6tr", "Integrity": "r0E4W2Km71NWxH8ibDFEVMricBFMprQvB6rnSZiRuVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250801_164559_0.sql", "FileLength": 58155520, "LastWriteTime": "2025-08-01T16:45:59.7423648+00:00"}, "0yV4o6YSCCEP70X+Hn3yDEhn97qY3qVV7FvqspinIwY=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\0146d796cb7b49d380177080e7821c73.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/0146d796cb7b49d380177080e7821c73#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\0146d796cb7b49d380177080e7821c73.webp", "FileLength": 97050, "LastWriteTime": "2025-08-01T16:53:17.3462136+00:00"}, "asqLdl7X4DRU0tCEJ4KJ9w9CsGsk8oNHmyt/GGQ254Y=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6fe2ac6fc73c41258e0d23a6d98b04b3.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6fe2ac6fc73c41258e0d23a6d98b04b3#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6fe2ac6fc73c41258e0d23a6d98b04b3.png", "FileLength": 195627, "LastWriteTime": "2025-08-01T16:53:20.7225902+00:00"}, "R2tn7qbd9zAGmggeT7J6H4+vLmj3z7DmOih0bJ2/vWE=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3d52fa12f954466ab7ffb05f6080f821.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/3d52fa12f954466ab7ffb05f6080f821#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\3d52fa12f954466ab7ffb05f6080f821.webp", "FileLength": 97050, "LastWriteTime": "2025-08-01T17:48:23.1758503+00:00"}}, "CachedCopyCandidates": {}}